import express from 'express';
import multer from 'multer';
import importService from '../services/importService';
import { ClienteModel } from '../models/Cliente';
import { MotocicletaModel } from '../models/Motocicleta';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    const allowedExtensions = ['.csv', '.xls', '.xlsx'];
    const hasValidExtension = allowedExtensions.some(ext => 
      file.originalname.toLowerCase().endsWith(ext)
    );
    
    if (allowedTypes.includes(file.mimetype) || hasValidExtension) {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos CSV, XLS e XLSX são permitidos'));
    }
  }
});

// POST /api/import/preview - Preview import data
router.post('/preview', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo foi enviado'
      });
    }

    const preview = await importService.generatePreview(
      req.file.buffer,
      req.file.originalname
    );

    res.json({
      success: true,
      data: preview
    });
  } catch (error: any) {
    console.error('Error generating preview:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Erro ao processar arquivo'
    });
  }
});

// POST /api/import/clientes - Import clients
router.post('/clientes', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo foi enviado'
      });
    }

    // Process import
    const result = await importService.processImport(
      req.file.buffer,
      req.file.originalname,
      'clientes'
    );

    if (result.data && result.data.length > 0) {
      // Save valid data to database
      const savedClientes = [];
      const saveErrors = [];

      for (const clienteData of result.data) {
        try {
          const clienteId = await ClienteModel.create(clienteData);
          savedClientes.push({ id: clienteId, ...clienteData });
        } catch (error: any) {
          saveErrors.push({
            data: clienteData,
            error: error.message
          });
        }
      }

      res.json({
        success: true,
        message: `Importação concluída: ${savedClientes.length} clientes salvos`,
        data: {
          ...result,
          savedCount: savedClientes.length,
          saveErrors: saveErrors.length,
          savedData: savedClientes
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Nenhum dado válido encontrado para importar',
        data: result
      });
    }
  } catch (error: any) {
    console.error('Error importing clients:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erro ao importar clientes'
    });
  }
});

// POST /api/import/motocicletas - Import motorcycles
router.post('/motocicletas', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo foi enviado'
      });
    }

    // Process import
    const result = await importService.processImport(
      req.file.buffer,
      req.file.originalname,
      'motocicletas'
    );

    if (result.data && result.data.length > 0) {
      // Save valid data to database
      const savedMotocicletas = [];
      const saveErrors = [];

      for (const motoData of result.data) {
        try {
          const motoId = await MotocicletaModel.create(motoData);
          savedMotocicletas.push({ id: motoId, ...motoData });
        } catch (error: any) {
          saveErrors.push({
            data: motoData,
            error: error.message
          });
        }
      }

      res.json({
        success: true,
        message: `Importação concluída: ${savedMotocicletas.length} motocicletas salvas`,
        data: {
          ...result,
          savedCount: savedMotocicletas.length,
          saveErrors: saveErrors.length,
          savedData: savedMotocicletas
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Nenhum dado válido encontrado para importar',
        data: result
      });
    }
  } catch (error: any) {
    console.error('Error importing motorcycles:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erro ao importar motocicletas'
    });
  }
});

// GET /api/import/templates/clientes - Download client template
router.get('/templates/clientes', (req, res) => {
  try {
    const template = importService.generateClienteTemplate();
    
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="template_clientes.csv"');
    res.send(template);
  } catch (error) {
    console.error('Error generating client template:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao gerar template de clientes'
    });
  }
});

// GET /api/import/templates/motocicletas - Download motorcycle template
router.get('/templates/motocicletas', (req, res) => {
  try {
    const template = importService.generateMotocicletaTemplate();
    
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="template_motocicletas.csv"');
    res.send(template);
  } catch (error) {
    console.error('Error generating motorcycle template:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao gerar template de motocicletas'
    });
  }
});

// POST /api/import/validate - Validate import data without saving
router.post('/validate', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo foi enviado'
      });
    }

    const { type } = req.body;
    
    if (!type || !['clientes', 'motocicletas'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Tipo de importação inválido'
      });
    }

    const result = await importService.processImport(
      req.file.buffer,
      req.file.originalname,
      type
    );

    res.json({
      success: true,
      data: {
        ...result,
        data: undefined // Don't send the actual data, just validation results
      }
    });
  } catch (error: any) {
    console.error('Error validating import:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Erro ao validar arquivo'
    });
  }
});

export default router;
