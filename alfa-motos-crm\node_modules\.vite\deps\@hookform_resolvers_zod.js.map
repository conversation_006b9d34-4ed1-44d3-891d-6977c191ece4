{"version": 3, "sources": ["../../@hookform/resolvers/src/validateFieldsNatively.ts", "../../@hookform/resolvers/src/toNestErrors.ts", "../../@hookform/resolvers/zod/src/zod.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => names.some((n) => n.startsWith(name + '.'));\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport { ZodError, z } from 'zod';\nimport type { Resolver } from './types';\n\nconst isZodError = (error: any): error is ZodError =>\n  Array.isArray(error?.errors);\n\nconst parseErrorSchema = (\n  zodErrors: z.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n};\n\nexport const zodResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    try {\n      const data = await schema[\n        resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n      ](values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? values : data,\n      };\n    } catch (error: any) {\n      if (isZodError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.errors,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "mappings": ";;;;;;;;;AASA,IAAMA,IAAoBA,CACxBC,GACAC,IACAC,OAAAA;AAEA,MAAIF,KAAO,oBAAoBA,GAAK;AAClC,UAAMG,KAAQC,IAAIF,IAAQD,EAAAA;AAC1BD,MAAID,kBAAmBI,MAASA,GAAME,WAAY,EAAA,GAElDL,EAAIM,eAAAA;EACN;AAAA;AAVF,IAcaC,IAAyBA,CACpCL,IACAM,MAAAA;AAEA,aAAWP,MAAaO,EAAQC,QAAQ;AACtC,UAAMC,KAAQF,EAAQC,OAAOR,EAAAA;AACzBS,IAAAA,MAASA,GAAMV,OAAO,oBAAoBU,GAAMV,MAClDD,EAAkBW,GAAMV,KAAKC,IAAWC,EAAAA,IAC/BQ,GAAMC,QACfD,GAAMC,KAAKC,QAASZ,CAAAA,OAClBD,EAAkBC,IAAKC,IAAWC,EAAAA,CAAAA;EAGxC;AAAA;AA3BF,ICEaW,IAAeA,CAC1BX,IACAM,OAAAA;AAEAA,EAAAA,GAAQM,6BAA6BP,EAAuBL,IAAQM,EAAAA;AAEpE,QAAMO,IAAc,CAAA;AACpB,aAAWC,MAAQd,IAAQ;AACzB,UAAMQ,KAAQN,IAAII,GAAQC,QAAQO,EAAAA,GAC5Bb,IAAQc,OAAOC,OAAOhB,GAAOc,EAAAA,KAAS,CAAA,GAAI,EAC9ChB,KAAKU,MAASA,GAAMV,IAAAA,CAAAA;AAGtB,QAAImB,EAAmBX,GAAQY,SAASH,OAAOI,KAAKnB,EAAAA,GAASc,EAAAA,GAAO;AAClE,YAAMM,KAAmBL,OAAOC,OAAO,CAAA,GAAId,IAAIW,GAAaC,EAAAA,CAAAA;AAE5DO,UAAID,IAAkB,QAAQnB,CAAAA,GAC9BoB,IAAIR,GAAaC,IAAMM,EAAAA;IACzB,MACEC,KAAIR,GAAaC,IAAMb,CAAAA;EAE3B;AAEA,SAAOY;AAAAA;ADzBT,IC4BMI,IAAqBA,CACzBC,IACAI,MACGJ,GAAMK,KAAMC,CAAAA,OAAMA,GAAEC,WAAWH,IAAO,GAAA,CAAA;;;ACnC3C,IAGMI,IAAmB,SACvBC,IACAC,GAAAA;AAGA,WADMC,KAAqC,CAAE,GACtCF,GAAUG,UAAU;AACzB,QAAMC,KAAQJ,GAAU,CAAA,GAChBK,KAAwBD,GAAxBC,MAAMC,KAAkBF,GAAlBE,SACRC,IAD0BH,GAATI,KACJC,KAAK,GAAA;AAExB,QAAA,CAAKP,GAAOK,CAAAA,EACV,KAAI,iBAAiBH,IAAO;AAC1B,UAAMM,IAAaN,GAAMO,YAAY,CAAA,EAAGT,OAAO,CAAA;AAE/CA,MAAAA,GAAOK,CAAAA,IAAS,EACdD,SAASI,EAAWJ,SACpBM,MAAMF,EAAWL,KAAAA;IAErB,MACEH,CAAAA,GAAOK,CAAAA,IAAS,EAAED,SAAAA,IAASM,MAAMP,GAAAA;AAUrC,QANI,iBAAiBD,MACnBA,GAAMO,YAAYE,QAAQ,SAACH,IAAAA;AAAU,aACnCA,GAAWR,OAAOW,QAAQ,SAACC,IAAAA;AAAM,eAAAd,GAAUe,KAAKD,EAAAA;MAAE,CAAA;IAAC,CAAA,GAInDb,GAA0B;AAC5B,UAAMe,IAAQd,GAAOK,CAAAA,EAAOS,OACtBC,IAAWD,KAASA,EAAMZ,GAAMC,IAAAA;AAEtCH,MAAAA,GAAOK,CAAAA,IAASW,aACdX,GACAN,GACAC,IACAG,IACAY,IACK,CAAA,EAAgBE,OAAOF,GAAsBb,GAAME,OAAAA,IACpDF,GAAME,OAAAA;IAEd;AAEAN,IAAAA,GAAUoB,MAAAA;EACZ;AAEA,SAAOlB;AACT;AAnDA,IAqDamB,IACX,SAACC,IAAQC,IAAeC,IAAAA;AACjBC,SAAAA,WADiBD,OAAAA,KAAkB,CAAE,IACrCC,SAAAA,IAAQC,GAAGC,GAAAA;AAAW,QAAA;AAAA,aAAAC,QAAAC,QAAAA,SAAAA,GAAAA,IAAAA;AAAAA,YAAAA;AAAAA,cAAAA,KACvBD,QAAAC,QACiBP,GACQ,WAAzBE,GAAgBM,OAAkB,UAAU,YAAA,EAC5CL,IAAQF,EAAAA,CAAAA,EAAcQ,KAFlBC,SAAAA,IAAAA;AAMN,mBAFAL,EAAQM,6BAA6BC,EAAuB,CAAA,GAAIP,CAAAA,GAEzD,EACLzB,QAAQ,CAAiB,GACzBuB,QAAQD,GAAgBW,MAAMV,KAASO,GAAAA;UACvC,CAAA;QAAA,SAAAI,IAAA;AAAA,iBAAAC,GAAAD,EAAA;QAAA;AAAA,eAAAE,MAAAA,GAAA,OAAAA,GAAA,KAAA,QAAAD,EAAA,IAAAC;MAAA,EAVA,GAWH,SAAQlC,IAAAA;AACP,YApEa,SAACA,IAAAA;AAClB,iBAAAmC,MAAMC,QAAa,QAALpC,KAAAA,SAAAA,GAAOF,MAAAA;QAAO,EAmETE,EAAAA,EACb,QAAO,EACLqB,QAAQ,CAAA,GACRvB,QAAQuC,EACN1C,EACEK,GAAMF,QAAAA,CACLyB,EAAQM,6BACkB,UAAzBN,EAAQe,YAAAA,GAEZf,CAAAA,EAAAA;AAKN,cAAMvB;MACR,CAAA,CAAA;IACF,SAACU,IAAAA;AAAA,aAAAc,QAAAe,OAAA7B,EAAAA;IAAA;EAAA;AAAA;", "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "fields", "field", "refs", "for<PERSON>ach", "toNestErrors", "shouldUseNativeValidation", "fieldErrors", "path", "Object", "assign", "isNameInFieldArray", "names", "keys", "fieldArrayErrors", "set", "name", "some", "n", "startsWith", "parseErrorSchema", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "zodResolver", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "Promise", "resolve", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "r", "n", "a", "Array", "isArray", "toNestErrors", "criteriaMode", "reject"]}