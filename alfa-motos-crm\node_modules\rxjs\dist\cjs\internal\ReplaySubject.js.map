{"version": 3, "file": "ReplaySubject.js", "sourceRoot": "", "sources": ["../../../src/internal/ReplaySubject.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,qCAAoC;AAIpC,2EAA0E;AAgC1E;IAAsC,iCAAU;IAU9C,uBACU,WAAsB,EACtB,WAAsB,EACtB,kBAA6D;QAF7D,4BAAA,EAAA,sBAAsB;QACtB,4BAAA,EAAA,sBAAsB;QACtB,mCAAA,EAAA,qBAAwC,6CAAqB;QAHvE,YAKE,iBAAO,SAIR;QARS,iBAAW,GAAX,WAAW,CAAW;QACtB,iBAAW,GAAX,WAAW,CAAW;QACtB,wBAAkB,GAAlB,kBAAkB,CAA2C;QAZ/D,aAAO,GAAmB,EAAE,CAAC;QAC7B,yBAAmB,GAAG,IAAI,CAAC;QAcjC,KAAI,CAAC,mBAAmB,GAAG,WAAW,KAAK,QAAQ,CAAC;QACpD,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAC5C,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;;IAC9C,CAAC;IAED,4BAAI,GAAJ,UAAK,KAAQ;QACL,IAAA,KAA+E,IAAI,EAAjF,SAAS,eAAA,EAAE,OAAO,aAAA,EAAE,mBAAmB,yBAAA,EAAE,kBAAkB,wBAAA,EAAE,WAAW,iBAAS,CAAC;QAC1F,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,CAAC;SAC9E;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,iBAAM,IAAI,YAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IAGS,kCAAU,GAApB,UAAqB,UAAyB;QAC5C,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEhD,IAAA,KAAmC,IAAI,EAArC,mBAAmB,yBAAA,EAAE,OAAO,aAAS,CAAC;QAG9C,IAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvF,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAM,CAAC,CAAC;SAC/B;QAED,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAEzC,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,mCAAW,GAAnB;QACQ,IAAA,KAAoE,IAAI,EAAtE,WAAW,iBAAA,EAAE,kBAAkB,wBAAA,EAAE,OAAO,aAAA,EAAE,mBAAmB,yBAAS,CAAC;QAK/E,IAAM,kBAAkB,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;QACvE,WAAW,GAAG,QAAQ,IAAI,kBAAkB,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,kBAAkB,CAAC,CAAC;QAIxH,IAAI,CAAC,mBAAmB,EAAE;YACxB,IAAM,GAAG,GAAG,kBAAkB,CAAC,GAAG,EAAE,CAAC;YACrC,IAAI,IAAI,GAAG,CAAC,CAAC;YAGb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,IAAK,OAAO,CAAC,CAAC,CAAY,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC3E,IAAI,GAAG,CAAC,CAAC;aACV;YACD,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;SACrC;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAzED,CAAsC,iBAAO,GAyE5C;AAzEY,sCAAa"}