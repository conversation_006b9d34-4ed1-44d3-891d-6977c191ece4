{"name": "alfa-motos-crm", "private": true, "version": "1.0.0", "type": "module", "description": "Sistema CRM para Alfa Motos - Gestão de clientes e motocicletas", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:prod": "NODE_ENV=production npm run build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "deploy": "node scripts/deploy.js", "deploy:check": "npm run lint && npm run build", "clean": "rm -rf dist node_modules/.vite", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "server:dev": "nodemon --exec ts-node server/index.ts", "server:build": "tsc --project server/tsconfig.json", "server:start": "node server/dist/index.js", "db:setup": "mysql -u root -p < database/schema.sql", "db:seed": "mysql -u root -p alfa_motos_crm < database/sample_data.sql"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "clsx": "^2.0.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "date-fns": "^2.30.0", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "multer": "^2.0.1", "mysql2": "^3.14.2", "papaparse": "^5.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.13", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "concurrently": "^9.2.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "nodemon": "^3.1.10", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^5.0.0"}}