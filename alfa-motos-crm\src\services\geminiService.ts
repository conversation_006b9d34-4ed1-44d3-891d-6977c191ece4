import { GoogleGenerativeAI } from '@google/generative-ai';
import { <PERSON><PERSON>e, Moto<PERSON>cle<PERSON> } from '../types';

export interface AnalysisRequest {
  type: 'client_analysis' | 'motorcycle_analysis' | 'general_report' | 'trend_analysis' | 'risk_assessment';
  data: any;
  context?: string;
}

export interface AnalysisResponse {
  summary: string;
  insights: string[];
  recommendations: string[];
  charts?: ChartData[];
  metrics?: Metric[];
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'area';
  title: string;
  data: any[];
  description: string;
}

export interface Metric {
  label: string;
  value: string | number;
  trend: 'up' | 'down' | 'stable';
  description: string;
}

class GeminiService {
  private genAI: GoogleGenerativeAI | null = null;
  private model: any = null;

  constructor() {
    this.initializeGemini();
  }

  private initializeGemini() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    
    if (!apiKey) {
      console.warn('Gemini API key not found. AI features will be disabled.');
      return;
    }

    try {
      this.genAI = new GoogleGenerativeAI(apiKey);
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
    } catch (error) {
      console.error('Failed to initialize Gemini AI:', error);
    }
  }

  async generateAnalysis(request: AnalysisRequest): Promise<AnalysisResponse> {
    if (!this.model) {
      throw new Error('Gemini AI not initialized. Please check your API key.');
    }

    try {
      const prompt = this.buildPrompt(request);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      return this.parseResponse(text, request.type);
    } catch (error) {
      console.error('Error generating analysis:', error);
      throw new Error('Failed to generate analysis. Please try again.');
    }
  }

  private buildPrompt(request: AnalysisRequest): string {
    const baseContext = `
Você é um analista especializado em gestão de motocicletas e clientes para a empresa Alfa Motos.
Analise os dados fornecidos e gere insights valiosos em português brasileiro.
Forneça sua resposta em formato JSON estruturado com as seguintes seções:
- summary: Resumo executivo da análise
- insights: Array de insights principais (máximo 5)
- recommendations: Array de recomendações práticas (máximo 5)
- metrics: Array de métricas importantes com valores e tendências

Dados para análise:
${JSON.stringify(request.data, null, 2)}

Contexto adicional: ${request.context || 'Nenhum contexto adicional fornecido'}
`;

    switch (request.type) {
      case 'client_analysis':
        return `${baseContext}

Foque na análise de clientes:
- Perfil demográfico
- Padrões de comportamento
- Segmentação de clientes
- Oportunidades de crescimento
- Riscos e alertas`;

      case 'motorcycle_analysis':
        return `${baseContext}

Foque na análise de motocicletas:
- Status de regularização
- Distribuição por marca/modelo
- Análise temporal de cadastros
- Identificação de irregularidades
- Tendências do mercado`;

      case 'general_report':
        return `${baseContext}

Gere um relatório geral abrangente:
- Visão geral do negócio
- Performance operacional
- Indicadores chave
- Tendências identificadas
- Plano de ação sugerido`;

      case 'trend_analysis':
        return `${baseContext}

Foque na análise de tendências:
- Padrões temporais
- Sazonalidade
- Crescimento/declínio
- Previsões futuras
- Fatores influenciadores`;

      case 'risk_assessment':
        return `${baseContext}

Foque na avaliação de riscos:
- Identificação de riscos
- Probabilidade e impacto
- Medidas preventivas
- Planos de contingência
- Monitoramento contínuo`;

      default:
        return baseContext;
    }
  }

  private parseResponse(text: string, type: string): AnalysisResponse {
    try {
      // Tentar extrair JSON da resposta
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          summary: parsed.summary || 'Análise gerada com sucesso',
          insights: parsed.insights || [],
          recommendations: parsed.recommendations || [],
          metrics: parsed.metrics || []
        };
      }
    } catch (error) {
      console.warn('Failed to parse JSON response, using fallback parsing');
    }

    // Fallback: parsing manual se JSON falhar
    return this.fallbackParsing(text, type);
  }

  private fallbackParsing(text: string, type: string): AnalysisResponse {
    const lines = text.split('\n').filter(line => line.trim());
    
    return {
      summary: lines[0] || 'Análise gerada com sucesso',
      insights: this.extractBulletPoints(text, ['insight', 'observação', 'destaque']),
      recommendations: this.extractBulletPoints(text, ['recomendação', 'sugestão', 'ação']),
      metrics: []
    };
  }

  private extractBulletPoints(text: string, keywords: string[]): string[] {
    const points: string[] = [];
    const lines = text.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('-') || trimmed.startsWith('•') || trimmed.startsWith('*')) {
        points.push(trimmed.substring(1).trim());
      } else if (keywords.some(keyword => trimmed.toLowerCase().includes(keyword))) {
        points.push(trimmed);
      }
    }

    return points.slice(0, 5); // Limitar a 5 pontos
  }

  // Análises específicas para diferentes tipos de dados
  async analyzeClients(clientes: Cliente[]): Promise<AnalysisResponse> {
    const analysisData = {
      total: clientes.length,
      status_distribution: this.getStatusDistribution(clientes),
      recent_registrations: clientes.filter(c => 
        new Date(c.dataCadastro) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      ).length,
      cities: this.getCityDistribution(clientes),
      sample_data: clientes.slice(0, 5) // Amostra para análise
    };

    return this.generateAnalysis({
      type: 'client_analysis',
      data: analysisData,
      context: 'Análise da base de clientes da Alfa Motos'
    });
  }

  async analyzeMotorcycles(motos: Motocicleta[]): Promise<AnalysisResponse> {
    const analysisData = {
      total: motos.length,
      status_distribution: this.getMotorcycleStatusDistribution(motos),
      brand_distribution: this.getBrandDistribution(motos),
      year_distribution: this.getYearDistribution(motos),
      recent_consultations: motos.filter(m => 
        new Date(m.dataUltimaConsulta) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length,
      sample_data: motos.slice(0, 5)
    };

    return this.generateAnalysis({
      type: 'motorcycle_analysis',
      data: analysisData,
      context: 'Análise da frota de motocicletas gerenciada pela Alfa Motos'
    });
  }

  async generateGeneralReport(clientes: Cliente[], motos: Motocicleta[]): Promise<AnalysisResponse> {
    const analysisData = {
      clients: {
        total: clientes.length,
        active: clientes.filter(c => c.status === 'ativo').length,
        recent: clientes.filter(c => 
          new Date(c.dataCadastro) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        ).length
      },
      motorcycles: {
        total: motos.length,
        regular: motos.filter(m => m.status === 'regular').length,
        irregular: motos.filter(m => m.status === 'irregular').length,
        pending: motos.filter(m => m.status === 'pendente').length
      },
      business_metrics: {
        client_to_motorcycle_ratio: motos.length / clientes.length,
        regularization_rate: (motos.filter(m => m.status === 'regular').length / motos.length) * 100
      }
    };

    return this.generateAnalysis({
      type: 'general_report',
      data: analysisData,
      context: 'Relatório geral de performance da Alfa Motos'
    });
  }

  // Métodos auxiliares para distribuições
  private getStatusDistribution(clientes: Cliente[]) {
    const distribution: Record<string, number> = {};
    clientes.forEach(cliente => {
      distribution[cliente.status] = (distribution[cliente.status] || 0) + 1;
    });
    return distribution;
  }

  private getMotorcycleStatusDistribution(motos: Motocicleta[]) {
    const distribution: Record<string, number> = {};
    motos.forEach(moto => {
      distribution[moto.status] = (distribution[moto.status] || 0) + 1;
    });
    return distribution;
  }

  private getCityDistribution(clientes: Cliente[]) {
    const distribution: Record<string, number> = {};
    clientes.forEach(cliente => {
      const city = cliente.endereco.cidade;
      distribution[city] = (distribution[city] || 0) + 1;
    });
    return Object.entries(distribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10); // Top 10 cidades
  }

  private getBrandDistribution(motos: Motocicleta[]) {
    const distribution: Record<string, number> = {};
    motos.forEach(moto => {
      distribution[moto.marca] = (distribution[moto.marca] || 0) + 1;
    });
    return Object.entries(distribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10); // Top 10 marcas
  }

  private getYearDistribution(motos: Motocicleta[]) {
    const distribution: Record<number, number> = {};
    motos.forEach(moto => {
      distribution[moto.ano] = (distribution[moto.ano] || 0) + 1;
    });
    return Object.entries(distribution)
      .map(([year, count]) => ({ year: parseInt(year), count }))
      .sort((a, b) => b.year - a.year)
      .slice(0, 10); // Últimos 10 anos
  }

  // Verificar se o serviço está disponível
  isAvailable(): boolean {
    return this.model !== null;
  }

  // Obter status da configuração
  getStatus(): { available: boolean; message: string } {
    if (!this.model) {
      return {
        available: false,
        message: 'Gemini AI não configurado. Adicione VITE_GEMINI_API_KEY ao arquivo .env'
      };
    }

    return {
      available: true,
      message: 'Gemini AI configurado e pronto para uso'
    };
  }
}

// Instância singleton
const geminiService = new GeminiService();

export default geminiService;
