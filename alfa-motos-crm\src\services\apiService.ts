import { Cliente, Motocicle<PERSON> } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: any;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
}

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
    return response.json();
  }

  // Dashboard stats
  async getDashboardStats(): Promise<ApiResponse<any>> {
    return this.request('/dashboard/stats');
  }

  // Clientes
  async getClientes(params: PaginationParams = {}): Promise<ApiResponse<Cliente[]>> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.status) searchParams.append('status', params.status);

    const queryString = searchParams.toString();
    const endpoint = `/clientes${queryString ? `?${queryString}` : ''}`;
    
    return this.request<Cliente[]>(endpoint);
  }

  async getCliente(id: string): Promise<ApiResponse<Cliente>> {
    return this.request<Cliente>(`/clientes/${id}`);
  }

  async createCliente(cliente: Omit<Cliente, 'id' | 'dataCadastro' | 'dataAtualizacao'>): Promise<ApiResponse<{ id: string }>> {
    return this.request<{ id: string }>('/clientes', {
      method: 'POST',
      body: JSON.stringify(cliente),
    });
  }

  async updateCliente(id: string, cliente: Partial<Cliente>): Promise<ApiResponse<void>> {
    return this.request<void>(`/clientes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(cliente),
    });
  }

  async deleteCliente(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/clientes/${id}`, {
      method: 'DELETE',
    });
  }

  async getClienteStats(): Promise<ApiResponse<any>> {
    return this.request('/clientes/stats');
  }

  // Motocicletas
  async getMotocicletas(params: PaginationParams & { marca?: string } = {}): Promise<ApiResponse<Motocicleta[]>> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.status) searchParams.append('status', params.status);
    if (params.marca) searchParams.append('marca', params.marca);

    const queryString = searchParams.toString();
    const endpoint = `/motocicletas${queryString ? `?${queryString}` : ''}`;
    
    return this.request<Motocicleta[]>(endpoint);
  }

  async getMotocicleta(id: string): Promise<ApiResponse<Motocicleta>> {
    return this.request<Motocicleta>(`/motocicletas/${id}`);
  }

  async createMotocicleta(motocicleta: Omit<Motocicleta, 'id' | 'historicoStatus' | 'documentos'>): Promise<ApiResponse<{ id: string }>> {
    return this.request<{ id: string }>('/motocicletas', {
      method: 'POST',
      body: JSON.stringify(motocicleta),
    });
  }

  async updateMotocicleta(id: string, motocicleta: Partial<Motocicleta>): Promise<ApiResponse<void>> {
    return this.request<void>(`/motocicletas/${id}`, {
      method: 'PUT',
      body: JSON.stringify(motocicleta),
    });
  }

  async deleteMotocicleta(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/motocicletas/${id}`, {
      method: 'DELETE',
    });
  }

  async updateMotocicletaStatus(id: string, status: string, motivo?: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/motocicletas/${id}/status`, {
      method: 'POST',
      body: JSON.stringify({ status, motivo }),
    });
  }

  async getMotocicletaStats(): Promise<ApiResponse<any>> {
    return this.request('/motocicletas/stats');
  }

  async getBrandDistribution(): Promise<ApiResponse<any[]>> {
    return this.request('/motocicletas/brands');
  }

  // Utility methods
  async testConnection(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      console.error('API connection test failed:', error);
      return false;
    }
  }

  // Error handling helper
  handleApiError(error: any): string {
    if (error.message) {
      return error.message;
    }
    
    if (typeof error === 'string') {
      return error;
    }
    
    return 'Erro desconhecido na API';
  }
}

// Create singleton instance
const apiService = new ApiService();

export default apiService;
