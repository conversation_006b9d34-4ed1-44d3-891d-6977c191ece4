import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  Search,
  Filter,
  Bike,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  User,
  Calendar,
  Shield,
  Plus,
  Upload
} from 'lucide-react';
import { Moto<PERSON><PERSON><PERSON>, Cliente } from '../types';
import MotoActions from '../components/MotoActions';
import BulkMotoActions from '../components/BulkMotoActions';
import BulkImport from '../components/BulkImport';

// Mock data
const mockMotos: (Motocicleta & { cliente: Cliente })[] = [
  {
    id: '1',
    clienteId: '1',
    marca: 'Honda',
    modelo: 'CB 600F Hornet',
    ano: 2020,
    placa: 'ABC-1234',
    chassi: '9C2JC50001R000001',
    renavam: '12345678901',
    cor: 'Vermelha',
    status: 'regular',
    situacao: 'Licenciado',
    dataUltimaConsulta: '2024-01-10T09:00:00Z',
    historicoStatus: [],
    documentos: [],
    cliente: {
      id: '1',
      nome: '<PERSON>',
      email: '<EMAIL>',
      telefone: '(11) 99999-9999',
      cpf: '123.456.789-00',
      endereco: {
        rua: 'Rua das Flores, 123',
        numero: '123',
        bairro: 'Centro',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567'
      },
      dataCadastro: '2024-01-15',
      status: 'ativo'
    }
  },
  {
    id: '2',
    clienteId: '1',
    marca: 'Yamaha',
    modelo: 'MT-07',
    ano: 2019,
    placa: 'XYZ-5678',
    chassi: '9C6KE0110KR000001',
    renavam: '98765432109',
    cor: 'Azul',
    status: 'irregular',
    situacao: 'Licenciamento em atraso',
    dataUltimaConsulta: '2024-01-10T09:00:00Z',
    historicoStatus: [],
    documentos: [],
    cliente: {
      id: '1',
      nome: 'João Silva Santos',
      email: '<EMAIL>',
      telefone: '(11) 99999-9999',
      cpf: '123.456.789-00',
      endereco: {
        rua: 'Rua das Flores, 123',
        numero: '123',
        bairro: 'Centro',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567'
      },
      dataCadastro: '2024-01-15',
      status: 'ativo'
    }
  },
  {
    id: '3',
    clienteId: '2',
    marca: 'Kawasaki',
    modelo: 'Ninja 300',
    ano: 2021,
    placa: 'DEF-9012',
    chassi: '9C2JC50001R000002',
    renavam: '11122233344',
    cor: 'Verde',
    status: 'pendente',
    situacao: 'Aguardando verificação',
    dataUltimaConsulta: '2024-01-09T09:00:00Z',
    historicoStatus: [],
    documentos: [],
    cliente: {
      id: '2',
      nome: 'Maria Oliveira Costa',
      email: '<EMAIL>',
      telefone: '(11) 88888-8888',
      cpf: '987.654.321-00',
      endereco: {
        rua: 'Av. Paulista, 1000',
        numero: '1000',
        bairro: 'Bela Vista',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01310-100'
      },
      dataCadastro: '2024-02-20',
      status: 'ativo'
    }
  }
];

const Motocicletas: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('todos');
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [showImport, setShowImport] = useState(false);

  const { data: motos, isLoading } = useQuery({
    queryKey: ['motocicletas'],
    queryFn: () => Promise.resolve(mockMotos),
  });

  const filteredMotos = motos?.filter(moto => {
    const matchesSearch = moto.marca.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         moto.modelo.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         moto.placa.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         moto.cliente.nome.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'todos' || moto.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'regular':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'irregular':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'pendente':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'bloqueada':
        return <Shield className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      regular: 'bg-green-100 text-green-800',
      irregular: 'bg-red-100 text-red-800',
      pendente: 'bg-yellow-100 text-yellow-800',
      bloqueada: 'bg-red-100 text-red-800',
      roubada: 'bg-red-100 text-red-800'
    };
    return styles[status as keyof typeof styles] || styles.pendente;
  };

  const getStatusCount = (status: string) => {
    return motos?.filter(moto => moto.status === status).length || 0;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-16 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Motocicletas</h1>
          <p className="text-gray-500">Gerencie todas as motocicletas cadastradas</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Última verificação: {new Date().toLocaleString('pt-BR')}
          </div>
          <button
            type="button"
            onClick={() => setShowImport(true)}
            className="btn-secondary flex items-center space-x-2"
          >
            <Upload className="h-4 w-4" />
            <span>Importar</span>
          </button>
          <Link
            to="/motocicletas/nova"
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Nova Motocicleta</span>
          </Link>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Regulares</p>
              <p className="text-2xl font-semibold text-gray-900">{getStatusCount('regular')}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Irregulares</p>
              <p className="text-2xl font-semibold text-gray-900">{getStatusCount('irregular')}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pendentes</p>
              <p className="text-2xl font-semibold text-gray-900">{getStatusCount('pendente')}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Bike className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-2xl font-semibold text-gray-900">{motos?.length || 0}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Buscar por marca, modelo, placa ou cliente..."
              className="input pl-10 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              className="input w-auto"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              title="Filtrar por status"
              aria-label="Filtrar motocicletas por status"
            >
              <option value="todos">Todos os status</option>
              <option value="regular">Regular</option>
              <option value="irregular">Irregular</option>
              <option value="pendente">Pendente</option>
              <option value="bloqueada">Bloqueada</option>
              <option value="roubada">Roubada</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      <BulkMotoActions
        motocicletas={filteredMotos || []}
        selectedIds={selectedIds}
        onSelectionChange={setSelectedIds}
      />

      {/* Motorcycles List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {filteredMotos?.length || 0} motocicleta(s) encontrada(s)
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredMotos?.map((moto) => (
            <div key={moto.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <input
                      type="checkbox"
                      checked={selectedIds.includes(moto.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedIds([...selectedIds, moto.id]);
                        } else {
                          setSelectedIds(selectedIds.filter(id => id !== moto.id));
                        }
                      }}
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                      aria-label={`Selecionar ${moto.marca} ${moto.modelo} - ${moto.placa}`}
                    />
                  </div>
                  <div className="flex-shrink-0">
                    <Bike className="h-12 w-12 text-gray-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-lg font-medium text-gray-900">
                        {moto.marca} {moto.modelo} ({moto.ano})
                      </h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(moto.status)}`}>
                        <span className="flex items-center">
                          {getStatusIcon(moto.status)}
                          <span className="ml-1">{moto.status}</span>
                        </span>
                      </span>
                    </div>
                    <div className="mt-1 flex flex-col sm:flex-row sm:space-x-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <span className="font-medium">Placa:</span>
                        <span className="ml-1">{moto.placa}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="font-medium">Cor:</span>
                        <span className="ml-1">{moto.cor}</span>
                      </div>
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        <Link 
                          to={`/clientes/${moto.clienteId}`}
                          className="text-primary-600 hover:text-primary-500"
                        >
                          {moto.cliente.nome}
                        </Link>
                      </div>
                    </div>
                    <div className="mt-1 text-sm text-gray-600">
                      <span className="font-medium">Situação:</span> {moto.situacao}
                    </div>
                    <div className="mt-1 flex items-center text-xs text-gray-400">
                      <Calendar className="h-4 w-4 mr-1" />
                      Última consulta: {new Date(moto.dataUltimaConsulta).toLocaleString('pt-BR')}
                    </div>
                  </div>
                </div>
                
                <MotoActions
                  motocicleta={moto}
                  onEdit={() => window.location.href = `/motocicletas/${moto.id}/editar`}
                  onView={() => window.location.href = `/motocicletas/${moto.id}`}
                  onDelete={() => {
                    if (confirm(`Tem certeza que deseja excluir ${moto.marca} ${moto.modelo} - ${moto.placa}?`)) {
                      // Implementar exclusão
                      console.log('Excluir motocicleta:', moto.id);
                    }
                  }}
                  compact={true}
                />
              </div>
            </div>
          ))}
        </div>

        {filteredMotos?.length === 0 && (
          <div className="p-12 text-center">
            <Bike className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma motocicleta encontrada</h3>
            <p className="text-gray-500">Tente ajustar os filtros de busca.</p>
          </div>
        )}
      </div>

      {/* Bulk Import Modal */}
      {showImport && (
        <BulkImport
          type="motocicletas"
          onImportComplete={(result) => {
            console.log('Import completed:', result);
            // Refresh data here if needed
          }}
          onClose={() => setShowImport(false)}
        />
      )}
    </div>
  );
};

export default Motocicletas;
