import React, { useState } from 'react';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  Minus,
  Lightbulb,
  Target,
  BarChart3,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { AnalysisResponse, Metric } from '../services/geminiService';

interface AIAnalysisProps {
  analysis: AnalysisResponse | null;
  loading: boolean;
  error: string | null;
  onRefresh?: () => void;
  title?: string;
}

const AIAnalysis: React.FC<AIAnalysisProps> = ({
  analysis,
  loading,
  error,
  onRefresh,
  title = 'Análise Inteligente'
}) => {
  const [expandedSection, setExpandedSection] = useState<string | null>('summary');

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'text-green-600 bg-green-50';
      case 'down':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Brain className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          </div>
        </div>
        <div className="card-body">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 text-purple-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Gerando análise inteligente...</p>
              <p className="text-sm text-gray-500 mt-2">Isso pode levar alguns segundos</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            </div>
            {onRefresh && (
              <button
                onClick={onRefresh}
                className="btn-secondary btn-sm"
              >
                <RefreshCw className="h-4 w-4" />
                Tentar Novamente
              </button>
            )}
          </div>
        </div>
        <div className="card-body">
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">Erro na Análise</h4>
            <p className="text-gray-600 mb-4">{error}</p>
            {onRefresh && (
              <button onClick={onRefresh} className="btn-primary">
                Tentar Novamente
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (!analysis) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gray-100 rounded-lg">
              <Brain className="h-5 w-5 text-gray-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          </div>
        </div>
        <div className="card-body">
          <div className="text-center py-8">
            <Info className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">Nenhuma Análise Disponível</h4>
            <p className="text-gray-600">Clique em "Gerar Análise" para começar</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Brain className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          </div>
          {onRefresh && (
            <button
              onClick={onRefresh}
              className="btn-secondary btn-sm"
            >
              <RefreshCw className="h-4 w-4" />
              Atualizar
            </button>
          )}
        </div>
      </div>

      <div className="card-body space-y-6">
        {/* Resumo Executivo */}
        <div>
          <button
            onClick={() => toggleSection('summary')}
            className="w-full flex items-center justify-between p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-900">Resumo Executivo</span>
            </div>
            <div className={`transform transition-transform ${expandedSection === 'summary' ? 'rotate-180' : ''}`}>
              ▼
            </div>
          </button>
          {expandedSection === 'summary' && (
            <div className="mt-3 p-4 bg-white border border-blue-200 rounded-lg">
              <p className="text-gray-700 leading-relaxed">{analysis.summary}</p>
            </div>
          )}
        </div>

        {/* Métricas */}
        {analysis.metrics && analysis.metrics.length > 0 && (
          <div>
            <button
              onClick={() => toggleSection('metrics')}
              className="w-full flex items-center justify-between p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-900">Métricas Principais</span>
              </div>
              <div className={`transform transition-transform ${expandedSection === 'metrics' ? 'rotate-180' : ''}`}>
                ▼
              </div>
            </button>
            {expandedSection === 'metrics' && (
              <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                {analysis.metrics.map((metric, index) => (
                  <div key={index} className="p-4 bg-white border border-green-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-600">{metric.label}</span>
                      {getTrendIcon(metric.trend)}
                    </div>
                    <div className={`text-2xl font-bold mb-1 ${getTrendColor(metric.trend)}`}>
                      {metric.value}
                    </div>
                    <p className="text-xs text-gray-500">{metric.description}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Insights */}
        {analysis.insights && analysis.insights.length > 0 && (
          <div>
            <button
              onClick={() => toggleSection('insights')}
              className="w-full flex items-center justify-between p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
            >
              <div className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-600" />
                <span className="font-medium text-yellow-900">Insights Principais</span>
              </div>
              <div className={`transform transition-transform ${expandedSection === 'insights' ? 'rotate-180' : ''}`}>
                ▼
              </div>
            </button>
            {expandedSection === 'insights' && (
              <div className="mt-3 space-y-3">
                {analysis.insights.map((insight, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-white border border-yellow-200 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <p className="text-gray-700 text-sm">{insight}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Recomendações */}
        {analysis.recommendations && analysis.recommendations.length > 0 && (
          <div>
            <button
              onClick={() => toggleSection('recommendations')}
              className="w-full flex items-center justify-between p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <div className="flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-600" />
                <span className="font-medium text-purple-900">Recomendações</span>
              </div>
              <div className={`transform transition-transform ${expandedSection === 'recommendations' ? 'rotate-180' : ''}`}>
                ▼
              </div>
            </button>
            {expandedSection === 'recommendations' && (
              <div className="mt-3 space-y-3">
                {analysis.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-white border border-purple-200 rounded-lg">
                    <Target className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                    <p className="text-gray-700 text-sm">{recommendation}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AIAnalysis;
