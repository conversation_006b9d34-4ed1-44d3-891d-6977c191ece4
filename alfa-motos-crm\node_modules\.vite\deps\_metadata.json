{"hash": "4493b331", "configHash": "6cf75aab", "lockfileHash": "336cb5c1", "browserHash": "0ffcfa4b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "8233afd8", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "3ff3f51d", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "e6711853", "needsInterop": false}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "497ee824", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "bc132c12", "needsInterop": false}, "zod": {"src": "../../zod/index.js", "file": "zod.js", "fileHash": "8e5aefa7", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b10ff6af", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "496a51ec", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "3f5344c2", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "82e2ad9d", "needsInterop": false}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "99862cf4", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "96278172", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "53139614", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "df96d3b2", "needsInterop": false}, "date-fns/locale": {"src": "../../date-fns/esm/locale/index.js", "file": "date-fns_locale.js", "fileHash": "1f28f8f4", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "934a8d04", "needsInterop": true}, "@google/generative-ai": {"src": "../../@google/generative-ai/dist/index.mjs", "file": "@google_generative-ai.js", "fileHash": "85dd3463", "needsInterop": false}}, "chunks": {"chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-XUY6APUF": {"file": "chunk-XUY6APUF.js"}, "chunk-J6POAYNW": {"file": "chunk-J6POAYNW.js"}, "chunk-PDF5LE6H": {"file": "chunk-PDF5LE6H.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}