import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Cliente, Motocicleta, DashboardStats } from '../types';
import apiService from '../services/apiService';
import toast from 'react-hot-toast';
import { getGoogleSheetsService } from '../services/googleSheetsApi';
import n8nService from '../services/n8nService';
import notificationService from '../services/notificationService';
import googleFormsService from '../services/googleFormsService';

// Hook para dados do dashboard
export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async (): Promise<DashboardStats> => {
      try {
        const sheetsService = getGoogleSheetsService();
        const [clientes, motos] = await Promise.all([
          sheetsService.getClientes(),
          sheetsService.getMotocicletas()
        ]);

        const motosRegulares = motos.filter(m => m.status === 'regular').length;
        const motosIrregulares = motos.filter(m => m.status === 'irregular').length;
        const clientesAtivos = clientes.filter(c => c.status === 'ativo').length;
        const alertasRecentes = notificationService.getUnreadCount();

        // Calcular cadastros de hoje
        const hoje = new Date().toDateString();
        const cadastrosHoje = clientes.filter(c => 
          new Date(c.dataCadastro).toDateString() === hoje
        ).length;

        return {
          totalClientes: clientes.length,
          totalMotos: motos.length,
          motosRegulares,
          motosIrregulares,
          clientesAtivos,
          alertasRecentes,
          cadastrosHoje,
          verificacoesHoje: motos.length // Assumindo que todas foram verificadas
        };
      } catch (error) {
        // Retornar dados mock em caso de erro
        return {
          totalClientes: 1247,
          totalMotos: 1389,
          motosRegulares: 1156,
          motosIrregulares: 233,
          clientesAtivos: 1198,
          alertasRecentes: notificationService.getUnreadCount(),
          cadastrosHoje: 8,
          verificacoesHoje: 1389
        };
      }
    },
    refetchInterval: 30000, // Atualizar a cada 30 segundos
  });
};

// Hook para lista de clientes
export const useClientes = () => {
  return useQuery({
    queryKey: ['clientes'],
    queryFn: async (): Promise<Cliente[]> => {
      try {
        const sheetsService = getGoogleSheetsService();
        return await sheetsService.getClientes();
      } catch (error) {
        console.error('Erro ao carregar clientes:', error);
        // Retornar dados mock em caso de erro
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para um cliente específico
export const useCliente = (id: string) => {
  return useQuery({
    queryKey: ['cliente', id],
    queryFn: async (): Promise<Cliente | null> => {
      try {
        const sheetsService = getGoogleSheetsService();
        const clientes = await sheetsService.getClientes();
        return clientes.find(c => c.id === id) || null;
      } catch (error) {
        console.error('Erro ao carregar cliente:', error);
        return null;
      }
    },
    enabled: !!id,
  });
};

// Hook para lista de motocicletas
export const useMotocicletas = () => {
  return useQuery({
    queryKey: ['motocicletas'],
    queryFn: async (): Promise<Motocicleta[]> => {
      try {
        const sheetsService = getGoogleSheetsService();
        return await sheetsService.getMotocicletas();
      } catch (error) {
        console.error('Erro ao carregar motocicletas:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para uma motocicleta específica
export const useMotocicleta = (id: string) => {
  return useQuery({
    queryKey: ['motocicleta', id],
    queryFn: async (): Promise<Motocicleta | null> => {
      try {
        const sheetsService = getGoogleSheetsService();
        const motos = await sheetsService.getMotocicletas();
        return motos.find(m => m.id === id) || null;
      } catch (error) {
        console.error('Erro ao carregar motocicleta:', error);
        return null;
      }
    },
    enabled: !!id,
  });
};

// Hook para motocicletas de um cliente
export const useClienteMotos = (clienteId: string) => {
  return useQuery({
    queryKey: ['cliente-motos', clienteId],
    queryFn: async (): Promise<Motocicleta[]> => {
      try {
        const sheetsService = getGoogleSheetsService();
        const motos = await sheetsService.getMotocicletas();
        return motos.filter(m => m.clienteId === clienteId);
      } catch (error) {
        console.error('Erro ao carregar motocicletas do cliente:', error);
        return [];
      }
    },
    enabled: !!clienteId,
  });
};

// Mutation para adicionar cliente
export const useAddCliente = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (cliente: Omit<Cliente, 'id'>): Promise<string> => {
      const sheetsService = getGoogleSheetsService();
      const clienteId = await sheetsService.adicionarCliente(cliente);
      
      // Notificar n8n sobre novo cliente
      await n8nService.sendToN8n({
        tipo: 'novo_cadastro',
        timestamp: new Date().toISOString(),
        cliente: { ...cliente, id: clienteId },
        motocicleta: {}
      });

      return clienteId;
    },
    onSuccess: () => {
      // Invalidar cache dos clientes
      queryClient.invalidateQueries({ queryKey: ['clientes'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
    },
  });
};

// Mutation para adicionar motocicleta
export const useAddMotocicleta = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (moto: Omit<Motocicleta, 'id'>): Promise<string> => {
      const sheetsService = getGoogleSheetsService();
      const motoId = await sheetsService.adicionarMotocicleta(moto);
      
      // Notificar n8n sobre nova motocicleta
      await n8nService.sendToN8n({
        tipo: 'novo_cadastro',
        timestamp: new Date().toISOString(),
        cliente: { id: moto.clienteId },
        motocicleta: { ...moto, id: motoId }
      });

      return motoId;
    },
    onSuccess: () => {
      // Invalidar cache das motocicletas
      queryClient.invalidateQueries({ queryKey: ['motocicletas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
    },
  });
};

// Mutation para atualizar status de motocicleta
export const useUpdateMotoStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      motoId, 
      status, 
      situacao 
    }: { 
      motoId: string; 
      status: string; 
      situacao: string; 
    }): Promise<void> => {
      const sheetsService = getGoogleSheetsService();
      await sheetsService.atualizarStatusMoto(motoId, status, situacao);
      
      // Notificar n8n sobre mudança de status
      await n8nService.sendToN8n({
        tipo: 'status_change',
        timestamp: new Date().toISOString(),
        cliente: {},
        motocicleta: { id: motoId, status, situacao }
      });
    },
    onSuccess: () => {
      // Invalidar cache das motocicletas
      queryClient.invalidateQueries({ queryKey: ['motocicletas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
    },
  });
};

// Mutation para solicitar verificação manual
export const useRequestVerification = () => {
  return useMutation({
    mutationFn: async (motocicletaId: string): Promise<boolean> => {
      return await n8nService.requestManualVerification(motocicletaId);
    },
  });
};

// Mutation para sincronizar todos os dados
export const useSyncAllData = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (): Promise<boolean> => {
      return await n8nService.syncAllData();
    },
    onSuccess: () => {
      // Invalidar todo o cache
      queryClient.invalidateQueries();
    },
  });
};

// Hook para testar conexão com Google Sheets
export const useTestGoogleSheetsConnection = () => {
  return useMutation({
    mutationFn: async (): Promise<boolean> => {
      const sheetsService = getGoogleSheetsService();
      return await sheetsService.testarConexao();
    },
  });
};

// Hook para testar conexão com n8n
export const useTestN8nConnection = () => {
  return useMutation({
    mutationFn: async (): Promise<boolean> => {
      return await n8nService.testConnection();
    },
  });
};

// Hook para processar respostas do Google Forms
export const useProcessFormResponses = () => {
  return useMutation({
    mutationFn: async (): Promise<number> => {
      return await googleFormsService.processNewResponses();
    },
  });
};

// Hook para obter estatísticas do Google Forms
export const useFormStats = () => {
  return useQuery({
    queryKey: ['form-stats'],
    queryFn: async () => {
      return await googleFormsService.getFormStats();
    },
    refetchInterval: 60000, // Atualizar a cada minuto
  });
};

// Mutation para atualizar cliente
export const useUpdateCliente = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Cliente> }): Promise<void> => {
      // Simular atualização (implementar com API real)
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Atualizando cliente:', id, data);
    },
    onSuccess: (_, { id }) => {
      // Invalidar cache do cliente específico e lista de clientes
      queryClient.invalidateQueries({ queryKey: ['cliente', id] });
      queryClient.invalidateQueries({ queryKey: ['clientes'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
    },
  });
};

// Mutation para atualizar motocicleta
export const useUpdateMotocicleta = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Motocicleta> }): Promise<void> => {
      // Simular atualização (implementar com API real)
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Atualizando motocicleta:', id, data);
    },
    onSuccess: (_, { id }) => {
      // Invalidar cache da motocicleta específica e lista de motocicletas
      queryClient.invalidateQueries({ queryKey: ['motocicleta', id] });
      queryClient.invalidateQueries({ queryKey: ['motocicletas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
    },
  });
};
