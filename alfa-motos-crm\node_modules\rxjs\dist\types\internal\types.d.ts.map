{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/types.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C;;;GAGG;AACH,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,iBAAiB;QACzB,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;KAC7B;CACF;AAID;;;;;;GAMG;AACH,MAAM,WAAW,aAAa,CAAC,CAAC,EAAE,CAAC;IACjC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;CAChB;AAED,MAAM,WAAW,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAE,SAAQ,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;CAAG;AAE9F,oBAAY,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAE9C;;;;;GAKG;AACH,MAAM,WAAW,wBAAwB,CAAC,CAAC,CAAE,SAAQ,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;CAAG;AAE9E;;;;;;GAMG;AACH,MAAM,WAAW,SAAS,CAAC,CAAC;IAC1B,KAAK,EAAE,CAAC,CAAC;IACT;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;CACnB;AAED;;;;;;GAMG;AACH,MAAM,WAAW,YAAY,CAAC,CAAC;IAC7B,KAAK,EAAE,CAAC,CAAC;IAET;;;;OAIG;IACH,QAAQ,EAAE,MAAM,CAAC;CAClB;AAID,MAAM,WAAW,cAAc;IAC7B,WAAW,IAAI,IAAI,CAAC;CACrB;AAED,oBAAY,aAAa,GAAG,YAAY,GAAG,cAAc,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AAEhF,MAAM,WAAW,gBAAiB,SAAQ,cAAc;IACtD,WAAW,IAAI,IAAI,CAAC;IACpB,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;CAC1B;AAED;;GAEG;AACH,oBAAY,qBAAqB,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAErH,4BAA4B;AAE5B,MAAM,WAAW,YAAY,CAAC,CAAC;IAC7B,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;CAC3D;AAED;;GAEG;AACH,oBAAY,eAAe,CAAC,CAAC,IACzB,UAAU,CAAC,CAAC,CAAC,GACb,iBAAiB,CAAC,CAAC,CAAC,GACpB,aAAa,CAAC,CAAC,CAAC,GAChB,WAAW,CAAC,CAAC,CAAC,GACd,SAAS,CAAC,CAAC,CAAC,GACZ,QAAQ,CAAC,CAAC,CAAC,GACX,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAE1B;;GAEG;AACH,oBAAY,cAAc,CAAC,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAErD;;GAEG;AACH,MAAM,WAAW,iBAAiB,CAAC,CAAC;IAClC,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC,CAAC;CAC5C;AAID;;;GAGG;AACH,MAAM,WAAW,gBAAgB,CAAC,CAAC;IACjC,2CAA2C;IAC3C,IAAI,EAAE,GAAG,CAAC;IACV,qCAAqC;IACrC,KAAK,EAAE,CAAC,CAAC;CACV;AAED;;;GAGG;AACH,MAAM,WAAW,iBAAiB;IAChC,2CAA2C;IAC3C,IAAI,EAAE,GAAG,CAAC;IACV,KAAK,EAAE,GAAG,CAAC;CACZ;AAED;;;GAGG;AACH,MAAM,WAAW,oBAAoB;IACnC,IAAI,EAAE,GAAG,CAAC;CACX;AAED;;GAEG;AACH,oBAAY,sBAAsB,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,GAAG,iBAAiB,GAAG,oBAAoB,CAAC;AAIvG,MAAM,WAAW,YAAY,CAAC,CAAC;IAC7B,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;IACzB,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,CAAC;IAC3B,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,aAAa,CAAC,CAAC;IAC9B,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;IAC1B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,CAAC;IAC1B,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,kBAAkB,CAAC,CAAC;IACnC,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;IAC1B,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,CAAC;IAC3B,QAAQ,EAAE,MAAM,IAAI,CAAC;CACtB;AAED,oBAAY,eAAe,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAE5F;;;;;;GAMG;AACH,MAAM,WAAW,QAAQ,CAAC,CAAC;IACzB;;;;;;OAMG;IACH,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;IACzB;;;;;;;;OAQG;IACH,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,CAAC;IAC1B;;;;;;;;OAQG;IACH,QAAQ,EAAE,MAAM,IAAI,CAAC;CACtB;AAED,MAAM,WAAW,WAAW,CAAC,CAAC,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;CAAG;AAIvE,MAAM,WAAW,aAAc,SAAQ,iBAAiB;IACtD,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,YAAY,CAAC;IACvG,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC;IACzG,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC;CAC3G;AAED,MAAM,WAAW,eAAe,CAAC,CAAC,CAAE,SAAQ,YAAY;IACtD,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,YAAY,CAAC;CACnD;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;;;;OAKG;IACH,GAAG,IAAI,MAAM,CAAC;CACf;AAED;;;;GAIG;AACH,oBAAY,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAEhF;;;;;;;GAOG;AACH,oBAAY,2BAA2B,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAEnG;;GAEG;AACH,oBAAY,uBAAuB,CAAC,CAAC,IAAI,2BAA2B,CAAC,CAAC,CAAC,CAAC;AAExE;;;;;GAKG;AACH,oBAAY,2BAA2B,CAAC,CAAC,IAAI;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,CAAC;AAEvF;;;;GAIG;AACH,oBAAY,oBAAoB,CAAC,CAAC,IAAI;KACnC,CAAC,IAAI,MAAM,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACtC,CAAC;AAEF;;;GAGG;AACH,oBAAY,IAAI,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;AAE5H;;;GAGG;AACH,oBAAY,IAAI,CAAC,CAAC,SAAS,SAAS,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;AAE7H;;;GAGG;AACH,oBAAY,IAAI,CAAC,CAAC,SAAS,SAAS,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;AAE3H;;;;GAIG;AACH,oBAAY,cAAc,CAAC,CAAC,SAAS,SAAS,OAAO,EAAE,IAAI,CAAC,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAEhG;;GAEG;AACH,oBAAY,qBAAqB,CAAC,CAAC,IAAI,CAAC,SAAS;IAAE,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;CAAE,GACtE,CAAC,SAAS,gBAAgB,CAAC,GAAG,CAAC,GAC7B,CAAC,SAAS;IAAE,KAAK,EAAE,MAAM,CAAC,CAAA;CAAE,GAC1B,CAAC,GACD,SAAS,GACX,KAAK,GACP,KAAK,CAAC;AAEV;;;;GAIG;AACH,oBAAY,KAAK,GAAG,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAEhE,oBAAY,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AAI3D,UAAU,+BAA+B,CAAC,CAAC;IAIzC,IAAI,IAAI,WAAW,CACf;QACE,IAAI,EAAE,KAAK,CAAC;QACZ,KAAK,EAAE,CAAC,CAAC;KACV,GACD;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,CAAC,EAAE,SAAS,CAAA;KAAE,CACpC,CAAC;IACF,WAAW,IAAI,IAAI,CAAC;CACrB;AAED;;;;GAIG;AACH,MAAM,WAAW,kBAAkB,CAAC,CAAC;IACnC,SAAS,IAAI,+BAA+B,CAAC,CAAC,CAAC,CAAC;CACjD;AAED;;;GAGG;AACH,MAAM,WAAW,WAAW,CAAC,CAAC,CAAE,SAAQ,UAAU,CAAC,CAAC,CAAC;IACnD;;;;;OAKG;IACH,OAAO,IAAI,YAAY,CAAC;CACzB"}