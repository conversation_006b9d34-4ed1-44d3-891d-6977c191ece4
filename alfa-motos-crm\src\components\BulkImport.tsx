import React, { useState, useRef } from 'react';
import {
  Upload,
  Download,
  FileText,
  AlertCircle,
  CheckCircle,
  X,
  Eye,
  RefreshCw,
  Users,
  Bike
} from 'lucide-react';
import toast from 'react-hot-toast';

interface ImportError {
  row: number;
  field: string;
  value: any;
  message: string;
}

interface ImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: ImportError[];
  savedCount?: number;
  saveErrors?: number;
}

interface ImportPreview {
  headers: string[];
  sampleData: any[];
  totalRows: number;
  detectedType: 'clientes' | 'motocicletas' | 'unknown';
}

interface BulkImportProps {
  type: 'clientes' | 'motocicletas';
  onImportComplete?: (result: ImportResult) => void;
  onClose?: () => void;
}

const BulkImport: React.FC<BulkImportProps> = ({ type, onImportComplete, onClose }) => {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'upload' | 'preview' | 'result'>('upload');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setPreview(null);
      setResult(null);
      setStep('upload');
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) {
      setFile(droppedFile);
      setPreview(null);
      setResult(null);
      setStep('upload');
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const generatePreview = async () => {
    if (!file) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE_URL}/import/preview`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        setPreview(data.data);
        setStep('preview');
      } else {
        toast.error(data.message || 'Erro ao gerar preview');
      }
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Erro ao processar arquivo');
    } finally {
      setLoading(false);
    }
  };

  const executeImport = async () => {
    if (!file) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE_URL}/import/${type}`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        setResult(data.data);
        setStep('result');
        toast.success(data.message);
        onImportComplete?.(data.data);
      } else {
        toast.error(data.message || 'Erro na importação');
        if (data.data) {
          setResult(data.data);
          setStep('result');
        }
      }
    } catch (error) {
      console.error('Error importing data:', error);
      toast.error('Erro ao importar dados');
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/import/templates/${type}`);
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `template_${type}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Template baixado com sucesso!');
    } catch (error) {
      console.error('Error downloading template:', error);
      toast.error('Erro ao baixar template');
    }
  };

  const reset = () => {
    setFile(null);
    setPreview(null);
    setResult(null);
    setStep('upload');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getTypeIcon = () => {
    return type === 'clientes' ? Users : Bike;
  };

  const getTypeLabel = () => {
    return type === 'clientes' ? 'Clientes' : 'Motocicletas';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              {React.createElement(getTypeIcon(), { className: "h-6 w-6 text-blue-600" })}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Importação em Lote - {getTypeLabel()}
              </h2>
              <p className="text-sm text-gray-500">
                Importe dados via arquivo CSV ou XLSX
              </p>
            </div>
          </div>
          {onClose && (
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-6 w-6" />
            </button>
          )}
        </div>

        <div className="p-6">
          {/* Step 1: Upload */}
          {step === 'upload' && (
            <div className="space-y-6">
              {/* Template Download */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Download className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-medium text-blue-900">Template de Importação</h3>
                    <p className="text-sm text-blue-700 mt-1">
                      Baixe o template com o formato correto para importação de {getTypeLabel().toLowerCase()}
                    </p>
                    <button
                      onClick={downloadTemplate}
                      className="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                    >
                      Baixar Template CSV
                    </button>
                  </div>
                </div>
              </div>

              {/* File Upload */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Selecione ou arraste seu arquivo
                </h3>
                <p className="text-gray-500 mb-4">
                  Formatos suportados: CSV, XLS, XLSX (máximo 10MB)
                </p>
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv,.xls,.xlsx"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="btn-primary"
                >
                  Selecionar Arquivo
                </button>
              </div>

              {/* Selected File */}
              {file && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-gray-600" />
                      <div>
                        <p className="font-medium text-gray-900">{file.name}</p>
                        <p className="text-sm text-gray-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={generatePreview}
                        disabled={loading}
                        className="btn-primary btn-sm"
                      >
                        {loading ? (
                          <>
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            Processando...
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4" />
                            Visualizar
                          </>
                        )}
                      </button>
                      <button onClick={reset} className="btn-secondary btn-sm">
                        Remover
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Preview */}
          {step === 'preview' && preview && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Preview dos Dados</h3>
                  <p className="text-sm text-gray-500">
                    {preview.totalRows} linha(s) detectada(s) • 
                    Tipo: {preview.detectedType === 'unknown' ? 'Não identificado' : preview.detectedType}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <button onClick={() => setStep('upload')} className="btn-secondary">
                    Voltar
                  </button>
                  <button
                    onClick={executeImport}
                    disabled={loading || preview.detectedType === 'unknown'}
                    className="btn-primary"
                  >
                    {loading ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Importando...
                      </>
                    ) : (
                      'Confirmar Importação'
                    )}
                  </button>
                </div>
              </div>

              {preview.detectedType === 'unknown' && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-900">Tipo não identificado</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        Não foi possível identificar automaticamente o tipo de dados. 
                        Verifique se o arquivo está no formato correto.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Sample Data Table */}
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        {preview.headers.map((header, index) => (
                          <th
                            key={index}
                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {preview.sampleData.map((row, rowIndex) => (
                        <tr key={rowIndex}>
                          {preview.headers.map((header, colIndex) => (
                            <td
                              key={colIndex}
                              className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate"
                            >
                              {row[header] || '-'}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Result */}
          {step === 'result' && result && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Resultado da Importação</h3>
                <button onClick={reset} className="btn-primary">
                  Nova Importação
                </button>
              </div>

              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-600">{result.totalRows}</div>
                  <div className="text-sm text-blue-700">Total de Linhas</div>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600">{result.savedCount || result.successCount}</div>
                  <div className="text-sm text-green-700">Importados</div>
                </div>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-red-600">{result.errorCount}</div>
                  <div className="text-sm text-red-700">Com Erro</div>
                </div>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-600">
                    {Math.round((result.savedCount || result.successCount) / result.totalRows * 100)}%
                  </div>
                  <div className="text-sm text-gray-700">Taxa de Sucesso</div>
                </div>
              </div>

              {/* Errors */}
              {result.errors && result.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-900 mb-3 flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    Erros Encontrados ({result.errors.length})
                  </h4>
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {result.errors.slice(0, 10).map((error, index) => (
                      <div key={index} className="text-sm text-red-700 bg-red-100 rounded p-2">
                        <strong>Linha {error.row}:</strong> {error.message} 
                        <span className="text-red-600"> (Campo: {error.field}, Valor: "{error.value}")</span>
                      </div>
                    ))}
                    {result.errors.length > 10 && (
                      <div className="text-sm text-red-600 italic">
                        ... e mais {result.errors.length - 10} erro(s)
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Success Message */}
              {result.success && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div>
                      <h4 className="font-medium text-green-900">Importação Concluída com Sucesso!</h4>
                      <p className="text-sm text-green-700 mt-1">
                        Todos os dados foram importados corretamente.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkImport;
