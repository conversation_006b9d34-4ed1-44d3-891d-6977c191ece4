import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  Search,
  Plus,
  Filter,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
  User,
  FileText,
  Bike,
  Upload
} from 'lucide-react';
import { Cliente } from '../types';
import DataTable from '../components/DataTable';
import BulkImport from '../components/BulkImport';

// Mock data
const mockClientes: Cliente[] = [
  {
    id: '1',
    nome: '<PERSON>',
    email: '<EMAIL>',
    telefone: '(11) 99999-9999',
    cpf: '123.456.789-00',
    endereco: {
      rua: 'Rua das Flores, 123',
      numero: '123',
      bairro: 'Centro',
      cidade: 'São Paulo',
      estado: 'SP',
      cep: '01234-567'
    },
    dataCadastro: '2024-01-15',
    status: 'ativo',
    observacoes: 'Cliente preferencial'
  },
  {
    id: '2',
    nome: '<PERSON>',
    email: '<EMAIL>',
    telefone: '(11) 88888-8888',
    cpf: '987.654.321-00',
    endereco: {
      rua: 'Av. Paulista, 1000',
      numero: '1000',
      bairro: 'Bela Vista',
      cidade: 'São Paulo',
      estado: 'SP',
      cep: '01310-100'
    },
    dataCadastro: '2024-02-20',
    status: 'ativo'
  },
  {
    id: '3',
    nome: 'Pedro Rodrigues Lima',
    email: '<EMAIL>',
    telefone: '(11) 77777-7777',
    cpf: '456.789.123-00',
    endereco: {
      rua: 'Rua Augusta, 500',
      numero: '500',
      bairro: 'Consolação',
      cidade: 'São Paulo',
      estado: 'SP',
      cep: '01305-000'
    },
    dataCadastro: '2024-03-10',
    status: 'pendente'
  }
];

const Clientes: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('todos');
  const [showDropdown, setShowDropdown] = useState<string | null>(null);
  const [showImport, setShowImport] = useState(false);

  // Definir colunas da tabela
  const columns = [
    {
      key: 'nome',
      label: 'Cliente',
      render: (value: string, row: Cliente) => (
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <User className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{row.cpf}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'contato',
      label: 'Contato',
      render: (_: any, row: Cliente) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail className="h-3 w-3" />
            {row.email}
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="h-3 w-3" />
            {row.telefone}
          </div>
        </div>
      ),
    },
    {
      key: 'endereco',
      label: 'Endereço',
      render: (_: any, row: Cliente) => (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <MapPin className="h-3 w-3" />
          <span>{row.endereco.cidade}, {row.endereco.estado}</span>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => (
        <span className={`badge ${getStatusBadge(value)}`}>
          {value}
        </span>
      ),
    },
    {
      key: 'acoes',
      label: 'Ações',
      render: (_: any, row: Cliente) => (
        <div className="flex items-center gap-2">
          <Link
            to={`/clientes/${row.id}/editar`}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:text-blue-700 transition-colors duration-200"
            title="Editar cliente"
          >
            <Edit className="h-4 w-4 mr-1" />
            Editar
          </Link>
          <Link
            to={`/clientes/${row.id}`}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 hover:text-gray-700 transition-colors duration-200"
            title="Ver detalhes"
          >
            <Eye className="h-4 w-4 mr-1" />
            Ver
          </Link>
        </div>
      ),
    },
  ];

  const { data: clientes, isLoading } = useQuery({
    queryKey: ['clientes'],
    queryFn: () => {
      // Aplicar atualizações do localStorage aos dados mock
      const clienteUpdates = JSON.parse(localStorage.getItem('clienteUpdates') || '{}');
      const clientesAtualizados = mockClientes.map(cliente => {
        if (clienteUpdates[cliente.id]) {
          return { ...cliente, ...clienteUpdates[cliente.id] };
        }
        return cliente;
      });
      return Promise.resolve(clientesAtualizados);
    },
  });

  const filteredClientes = clientes?.filter(cliente => {
    const matchesSearch = cliente.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cliente.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cliente.cpf.includes(searchTerm);
    const matchesStatus = statusFilter === 'todos' || cliente.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const styles = {
      ativo: 'bg-green-100 text-green-800',
      inativo: 'bg-gray-100 text-gray-800',
      pendente: 'bg-yellow-100 text-yellow-800'
    };
    return styles[status as keyof typeof styles] || styles.pendente;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="page-header">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="page-title">Clientes</h1>
              <p className="page-subtitle">
                Gerencie todos os clientes cadastrados no sistema
              </p>
            </div>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => setShowImport(true)}
                className="btn-secondary"
              >
                <Upload className="h-4 w-4" />
                Importar
              </button>
              <Link to="/clientes/novo" className="btn-primary">
                <Plus className="h-4 w-4" />
                Novo Cliente
              </Link>
              <Link to="/motocicletas/nova" className="btn-secondary">
                <Bike className="h-4 w-4" />
                Nova Moto
              </Link>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="card mb-6">
          <div className="card-body">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label className="label">Buscar clientes</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Nome, email ou CPF..."
                    className="input pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <label className="label">Status</label>
                <div className="relative">
                  <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <select
                    className="input pl-10"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    title="Filtrar por status"
                    aria-label="Filtrar clientes por status"
                  >
                    <option value="">Todos</option>
                    <option value="ativo">Ativo</option>
                    <option value="inativo">Inativo</option>
                    <option value="pendente">Pendente</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Clients Table */}
        <DataTable
          columns={columns}
          data={filteredClientes || []}
          loading={isLoading}
          emptyMessage="Nenhum cliente encontrado"
          className="animate-slide-up"
        />
      </div>

      {/* Bulk Import Modal */}
      {showImport && (
        <BulkImport
          type="clientes"
          onImportComplete={(result) => {
            console.log('Import completed:', result);
            // Refresh data here if needed
          }}
          onClose={() => setShowImport(false)}
        />
      )}
    </div>
  );
};

export default Clientes;
