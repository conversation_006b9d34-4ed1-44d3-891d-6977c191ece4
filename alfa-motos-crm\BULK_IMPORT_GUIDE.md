# 📊 Guia de Importação em Lote - Alfa Motos CRM

Este guia explica como usar a funcionalidade de importação em lote para clientes e motocicletas.

## 🚀 Funcionalidades

### ✅ **Formatos Suportados**
- **CSV** (Comma Separated Values)
- **XLS** (Excel 97-2003)
- **XLSX** (Excel 2007+)

### ✅ **Tipos de Importação**
- **Clientes** - Pessoas físicas e jurídicas
- **Motocicletas** - Dados completos das motos

### ✅ **Validações Automáticas**
- Campos obrigatórios
- Formatos de CPF/CNPJ
- Formatos de placa (antiga e Mercosul)
- RENAVAM (11 dígitos)
- <PERSON><PERSON> (17 caracteres)
- Email válido
- Ano da motocicleta

## 📋 Como Usar

### **1. Acessar a Importação**
- Vá para **Clientes** ou **Motocicletas**
- Clique no botão **"Importar"** no canto superior direito
- Uma janela modal será aberta

### **2. Bai<PERSON><PERSON>mplate**
- <PERSON><PERSON> em **"Baixar Template CSV"**
- Use este arquivo como base para seus dados
- Mantenha os cabeçalhos exatamente como no template

### **3. Preparar Arquivo**
- Preencha o template com seus dados
- Salve como CSV, XLS ou XLSX
- Máximo 10MB por arquivo

### **4. Fazer Upload**
- Arraste o arquivo para a área de upload OU
- Clique em **"Selecionar Arquivo"**
- Clique em **"Visualizar"** para preview

### **5. Revisar Preview**
- Verifique se os dados estão corretos
- Confirme o tipo detectado automaticamente
- Clique em **"Confirmar Importação"**

### **6. Verificar Resultado**
- Veja o resumo da importação
- Analise erros (se houver)
- Dados válidos são salvos automaticamente

## 📝 Templates de Importação

### **Template Clientes**
```csv
nome,email,telefone,cpf,cnpj,data_nascimento,profissao,renda_mensal,endereco_rua,endereco_numero,endereco_complemento,endereco_bairro,endereco_cidade,endereco_estado,endereco_cep,observacoes
João Silva Santos,<EMAIL>,(11) 99999-9999,123.456.789-00,,1985-05-15,Engenheiro,5000.00,Rua das Flores,123,Apto 45,Centro,São Paulo,SP,01234-567,Cliente preferencial
```

### **Template Motocicletas**
```csv
marca,modelo,ano,cor,combustivel,categoria,placa,chassi,renavam,proprietario_nome,proprietario_documento,proprietario_telefone,proprietario_email,cliente_id,observacoes
Honda,CB 600F Hornet,2020,Azul,gasolina,particular,ABC-1234,9C2JC50001R000001,12345678901,João Silva Santos,123.456.789-00,(11) 99999-9999,<EMAIL>,1,Motocicleta em excelente estado
```

## ⚠️ Regras de Validação

### **Clientes**
| Campo | Obrigatório | Formato | Observações |
|-------|-------------|---------|-------------|
| nome | ✅ | Texto | Nome completo |
| email | ❌ | <EMAIL> | Deve ser válido se preenchido |
| cpf | ✅* | 123.456.789-00 | *CPF OU CNPJ obrigatório |
| cnpj | ✅* | 12.345.678/0001-90 | *CPF OU CNPJ obrigatório |
| telefone | ❌ | (11) 99999-9999 | Formato livre |
| endereco_* | ❌ | Texto | Endereço completo recomendado |

### **Motocicletas**
| Campo | Obrigatório | Formato | Observações |
|-------|-------------|---------|-------------|
| marca | ✅ | Texto | Honda, Yamaha, etc. |
| modelo | ✅ | Texto | CB 600F, YZF-R3, etc. |
| placa | ✅ | ABC-1234 ou ABC1D23 | Antiga ou Mercosul |
| chassi | ✅ | 17 caracteres | Exatamente 17 caracteres |
| renavam | ✅ | 12345678901 | Exatamente 11 dígitos |
| ano | ❌ | 2020 | Entre 1900 e ano atual+1 |
| cor | ❌ | Texto | Azul, Vermelha, etc. |
| combustivel | ❌ | gasolina/etanol/flex/eletrica | Padrão: gasolina |
| categoria | ❌ | particular/comercial/oficial | Padrão: particular |

## 🔧 Endpoints da API

### **Preview de Importação**
```http
POST /api/import/preview
Content-Type: multipart/form-data

file: [arquivo.csv/xlsx]
```

### **Importar Clientes**
```http
POST /api/import/clientes
Content-Type: multipart/form-data

file: [arquivo.csv/xlsx]
```

### **Importar Motocicletas**
```http
POST /api/import/motocicletas
Content-Type: multipart/form-data

file: [arquivo.csv/xlsx]
```

### **Baixar Templates**
```http
GET /api/import/templates/clientes
GET /api/import/templates/motocicletas
```

### **Validar Arquivo**
```http
POST /api/import/validate
Content-Type: multipart/form-data

file: [arquivo.csv/xlsx]
type: clientes|motocicletas
```

## 📊 Exemplo de Resposta

### **Sucesso**
```json
{
  "success": true,
  "message": "Importação concluída: 15 clientes salvos",
  "data": {
    "totalRows": 15,
    "successCount": 15,
    "errorCount": 0,
    "savedCount": 15,
    "errors": []
  }
}
```

### **Com Erros**
```json
{
  "success": false,
  "message": "Nenhum dado válido encontrado para importar",
  "data": {
    "totalRows": 10,
    "successCount": 7,
    "errorCount": 3,
    "savedCount": 7,
    "errors": [
      {
        "row": 2,
        "field": "cpf",
        "value": "123456789",
        "message": "Formato de CPF inválido"
      },
      {
        "row": 5,
        "field": "placa",
        "value": "INVALID",
        "message": "Formato de placa inválido (ex: ABC-1234 ou ABC1D23)"
      }
    ]
  }
}
```

## 🎯 Dicas e Boas Práticas

### **Preparação dos Dados**
1. **Use o template** - Sempre baixe e use o template oficial
2. **Teste pequeno** - Faça um teste com poucos registros primeiro
3. **Dados limpos** - Remova espaços extras e caracteres especiais
4. **Backup** - Faça backup antes de importações grandes

### **Formatação**
- **CPF/CNPJ**: Pode ter ou não pontuação (123.456.789-00 ou 12345678900)
- **Telefone**: Formato livre, mas recomendado (11) 99999-9999
- **Placa**: Aceita ABC-1234 (antiga) ou ABC1D23 (Mercosul)
- **RENAVAM**: Apenas números, exatamente 11 dígitos
- **Chassi**: Exatamente 17 caracteres alfanuméricos

### **Performance**
- **Lotes pequenos**: Máximo 1000 registros por vez
- **Conexão estável**: Importações grandes podem demorar
- **Horário adequado**: Evite horários de pico

### **Tratamento de Erros**
- **Revise erros**: Sempre verifique o relatório de erros
- **Corrija dados**: Ajuste os dados e reimporte apenas os com erro
- **Validação prévia**: Use a função de validação antes da importação

## 🚨 Limitações

- **Tamanho máximo**: 10MB por arquivo
- **Formatos**: Apenas CSV, XLS, XLSX
- **Duplicatas**: Sistema não verifica duplicatas automaticamente
- **Relacionamentos**: cliente_id deve existir para motocicletas
- **Encoding**: Use UTF-8 para caracteres especiais

## 📞 Suporte

Se encontrar problemas:
1. Verifique se o arquivo está no formato correto
2. Confirme se todos os campos obrigatórios estão preenchidos
3. Teste com o template oficial
4. Verifique os logs de erro no resultado da importação
