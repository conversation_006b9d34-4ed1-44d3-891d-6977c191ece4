import * as XLSX from 'xlsx';
import csv from 'csv-parser';
import { Readable } from 'stream';
import { Cliente, Motocicleta } from '../../src/types/index';

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: ImportError[];
  data?: any[];
}

export interface ImportError {
  row: number;
  field: string;
  value: any;
  message: string;
}

export interface ImportPreview {
  headers: string[];
  sampleData: any[];
  totalRows: number;
  detectedType: 'clientes' | 'motocicletas' | 'unknown';
}

class ImportService {
  // Parse CSV file
  async parseCSV(buffer: Buffer): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      const stream = Readable.from(buffer.toString());
      
      stream
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', reject);
    });
  }

  // Parse XLSX file
  parseXLSX(buffer: Buffer): any[] {
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    return XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  }

  // Convert XLSX data to objects
  xlsxToObjects(data: any[]): any[] {
    if (data.length < 2) return [];
    
    const headers = data[0];
    const rows = data.slice(1);
    
    return rows.map(row => {
      const obj: any = {};
      headers.forEach((header: string, index: number) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });
  }

  // Detect file type based on headers
  detectDataType(headers: string[]): 'clientes' | 'motocicletas' | 'unknown' {
    const clienteHeaders = ['nome', 'email', 'cpf', 'cnpj', 'telefone'];
    const motoHeaders = ['marca', 'modelo', 'placa', 'chassi', 'renavam'];
    
    const normalizedHeaders = headers.map(h => h.toLowerCase().trim());
    
    const clienteMatches = clienteHeaders.filter(h => 
      normalizedHeaders.some(nh => nh.includes(h))
    ).length;
    
    const motoMatches = motoHeaders.filter(h => 
      normalizedHeaders.some(nh => nh.includes(h))
    ).length;
    
    if (clienteMatches >= 3) return 'clientes';
    if (motoMatches >= 3) return 'motocicletas';
    return 'unknown';
  }

  // Generate preview of import data
  async generatePreview(buffer: Buffer, filename: string): Promise<ImportPreview> {
    let data: any[] = [];
    
    if (filename.endsWith('.csv')) {
      data = await this.parseCSV(buffer);
    } else if (filename.endsWith('.xlsx') || filename.endsWith('.xls')) {
      const xlsxData = this.parseXLSX(buffer);
      data = this.xlsxToObjects(xlsxData);
    } else {
      throw new Error('Formato de arquivo não suportado');
    }

    if (data.length === 0) {
      throw new Error('Arquivo vazio ou formato inválido');
    }

    const headers = Object.keys(data[0]);
    const detectedType = this.detectDataType(headers);
    
    return {
      headers,
      sampleData: data.slice(0, 5), // First 5 rows for preview
      totalRows: data.length,
      detectedType
    };
  }

  // Validate cliente data
  validateCliente(data: any, rowIndex: number): ImportError[] {
    const errors: ImportError[] = [];
    
    // Required fields
    if (!data.nome || data.nome.trim() === '') {
      errors.push({
        row: rowIndex,
        field: 'nome',
        value: data.nome,
        message: 'Nome é obrigatório'
      });
    }

    // CPF or CNPJ required
    if (!data.cpf && !data.cnpj) {
      errors.push({
        row: rowIndex,
        field: 'cpf/cnpj',
        value: '',
        message: 'CPF ou CNPJ é obrigatório'
      });
    }

    // Validate CPF format
    if (data.cpf && !/^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$/.test(data.cpf)) {
      errors.push({
        row: rowIndex,
        field: 'cpf',
        value: data.cpf,
        message: 'Formato de CPF inválido'
      });
    }

    // Validate CNPJ format
    if (data.cnpj && !/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$|^\d{14}$/.test(data.cnpj)) {
      errors.push({
        row: rowIndex,
        field: 'cnpj',
        value: data.cnpj,
        message: 'Formato de CNPJ inválido'
      });
    }

    // Validate email
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push({
        row: rowIndex,
        field: 'email',
        value: data.email,
        message: 'Formato de email inválido'
      });
    }

    return errors;
  }

  // Validate motocicleta data
  validateMotocicleta(data: any, rowIndex: number): ImportError[] {
    const errors: ImportError[] = [];
    
    // Required fields
    const requiredFields = ['marca', 'modelo', 'placa', 'chassi', 'renavam'];
    
    requiredFields.forEach(field => {
      if (!data[field] || data[field].toString().trim() === '') {
        errors.push({
          row: rowIndex,
          field,
          value: data[field],
          message: `${field} é obrigatório`
        });
      }
    });

    // Validate plate format
    if (data.placa) {
      const plateRegex = /^[A-Z]{3}[-]?[0-9]{4}$|^[A-Z]{3}[0-9][A-Z][0-9]{2}$/;
      if (!plateRegex.test(data.placa.toUpperCase())) {
        errors.push({
          row: rowIndex,
          field: 'placa',
          value: data.placa,
          message: 'Formato de placa inválido (ex: ABC-1234 ou ABC1D23)'
        });
      }
    }

    // Validate RENAVAM (11 digits)
    if (data.renavam && !/^\d{11}$/.test(data.renavam.toString().replace(/\D/g, ''))) {
      errors.push({
        row: rowIndex,
        field: 'renavam',
        value: data.renavam,
        message: 'RENAVAM deve ter 11 dígitos'
      });
    }

    // Validate chassis (17 characters)
    if (data.chassi && data.chassi.toString().length !== 17) {
      errors.push({
        row: rowIndex,
        field: 'chassi',
        value: data.chassi,
        message: 'Chassi deve ter 17 caracteres'
      });
    }

    // Validate year
    if (data.ano) {
      const year = parseInt(data.ano);
      const currentYear = new Date().getFullYear();
      if (isNaN(year) || year < 1900 || year > currentYear + 1) {
        errors.push({
          row: rowIndex,
          field: 'ano',
          value: data.ano,
          message: `Ano deve estar entre 1900 e ${currentYear + 1}`
        });
      }
    }

    return errors;
  }

  // Transform data to match database schema
  transformClienteData(data: any): Partial<Cliente> {
    return {
      nome: data.nome?.trim(),
      email: data.email?.trim() || null,
      telefone: data.telefone?.trim() || null,
      cpf: data.cpf?.replace(/\D/g, '') || null,
      cnpj: data.cnpj?.replace(/\D/g, '') || null,
      tipoPessoa: data.cnpj ? 'juridica' : 'fisica',
      dataNascimento: data.data_nascimento || null,
      profissao: data.profissao?.trim() || null,
      rendaMensal: data.renda_mensal ? parseFloat(data.renda_mensal) : null,
      endereco: {
        rua: data.endereco_rua?.trim() || '',
        numero: data.endereco_numero?.trim() || '',
        complemento: data.endereco_complemento?.trim() || null,
        bairro: data.endereco_bairro?.trim() || '',
        cidade: data.endereco_cidade?.trim() || '',
        estado: data.endereco_estado?.trim() || '',
        cep: data.endereco_cep?.replace(/\D/g, '') || ''
      },
      status: data.status || 'ativo',
      observacoes: data.observacoes?.trim() || null
    };
  }

  // Transform motocicleta data
  transformMotocicletaData(data: any): Partial<Motocicleta> {
    return {
      clienteId: data.cliente_id || null,
      marca: data.marca?.trim(),
      modelo: data.modelo?.trim(),
      ano: data.ano ? parseInt(data.ano) : new Date().getFullYear(),
      cor: data.cor?.trim() || '',
      combustivel: data.combustivel || 'gasolina',
      categoria: data.categoria || 'particular',
      placa: data.placa?.toUpperCase().trim(),
      chassi: data.chassi?.toUpperCase().trim(),
      renavam: data.renavam?.replace(/\D/g, ''),
      proprietario: data.proprietario_nome?.trim() || null,
      proprietarioDocumento: data.proprietario_documento?.replace(/\D/g, '') || null,
      proprietarioTelefone: data.proprietario_telefone?.trim() || null,
      proprietarioEmail: data.proprietario_email?.trim() || null,
      status: data.status || 'pendente',
      situacao: data.situacao || 'Aguardando verificação',
      observacoes: data.observacoes?.trim() || null
    };
  }

  // Process import data
  async processImport(
    buffer: Buffer, 
    filename: string, 
    type: 'clientes' | 'motocicletas'
  ): Promise<ImportResult> {
    let data: any[] = [];
    
    // Parse file
    if (filename.endsWith('.csv')) {
      data = await this.parseCSV(buffer);
    } else if (filename.endsWith('.xlsx') || filename.endsWith('.xls')) {
      const xlsxData = this.parseXLSX(buffer);
      data = this.xlsxToObjects(xlsxData);
    } else {
      throw new Error('Formato de arquivo não suportado');
    }

    const errors: ImportError[] = [];
    const validData: any[] = [];

    // Validate and transform data
    data.forEach((row, index) => {
      let rowErrors: ImportError[] = [];
      
      if (type === 'clientes') {
        rowErrors = this.validateCliente(row, index + 1);
        if (rowErrors.length === 0) {
          validData.push(this.transformClienteData(row));
        }
      } else {
        rowErrors = this.validateMotocicleta(row, index + 1);
        if (rowErrors.length === 0) {
          validData.push(this.transformMotocicletaData(row));
        }
      }
      
      errors.push(...rowErrors);
    });

    return {
      success: errors.length === 0,
      totalRows: data.length,
      successCount: validData.length,
      errorCount: data.length - validData.length,
      errors,
      data: validData
    };
  }

  // Generate sample CSV template
  generateClienteTemplate(): string {
    const headers = [
      'nome',
      'email',
      'telefone',
      'cpf',
      'cnpj',
      'data_nascimento',
      'profissao',
      'renda_mensal',
      'endereco_rua',
      'endereco_numero',
      'endereco_complemento',
      'endereco_bairro',
      'endereco_cidade',
      'endereco_estado',
      'endereco_cep',
      'observacoes'
    ];

    const sampleData = [
      'João Silva Santos',
      '<EMAIL>',
      '(11) 99999-9999',
      '123.456.789-00',
      '',
      '1985-05-15',
      'Engenheiro',
      '5000.00',
      'Rua das Flores',
      '123',
      'Apto 45',
      'Centro',
      'São Paulo',
      'SP',
      '01234-567',
      'Cliente preferencial'
    ];

    return [headers.join(','), sampleData.join(',')].join('\n');
  }

  // Generate sample motocicleta template
  generateMotocicletaTemplate(): string {
    const headers = [
      'marca',
      'modelo',
      'ano',
      'cor',
      'combustivel',
      'categoria',
      'placa',
      'chassi',
      'renavam',
      'proprietario_nome',
      'proprietario_documento',
      'proprietario_telefone',
      'proprietario_email',
      'cliente_id',
      'observacoes'
    ];

    const sampleData = [
      'Honda',
      'CB 600F Hornet',
      '2020',
      'Azul',
      'gasolina',
      'particular',
      'ABC-1234',
      '9C2JC50001R000001',
      '12345678901',
      'João Silva Santos',
      '123.456.789-00',
      '(11) 99999-9999',
      '<EMAIL>',
      '1',
      'Motocicleta em excelente estado'
    ];

    return [headers.join(','), sampleData.join(',')].join('\n');
  }
}

// Export singleton instance
const importService = new ImportService();
export default importService;
