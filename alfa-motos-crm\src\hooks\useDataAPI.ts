import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Cliente, Motocicleta, DashboardStats } from '../types';
import apiService from '../services/apiService';
import toast from 'react-hot-toast';

// Hook para dados do dashboard
export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async (): Promise<DashboardStats> => {
      try {
        const response = await apiService.getDashboardStats();
        
        if (!response.success || !response.data) {
          throw new Error('Erro ao buscar estatísticas do dashboard');
        }

        return {
          totalClientes: response.data.totalClientes || 0,
          totalMotos: response.data.totalMotocicletas || 0,
          motosRegulares: response.data.motosRegulares || 0,
          motosIrregulares: response.data.motosIrregulares || 0,
          clientesAtivos: response.data.totalClientes || 0, // Assumindo que todos são ativos
          alertasRecentes: response.data.notificacoes || 0,
          cadastrosHoje: 0, // Será calculado no backend posteriormente
          verificacoesHoje: response.data.totalMotocicletas || 0
        };
      } catch (error) {
        console.error('Erro ao buscar estatísticas:', error);
        // Retornar dados padrão em caso de erro
        return {
          totalClientes: 0,
          totalMotos: 0,
          motosRegulares: 0,
          motosIrregulares: 0,
          clientesAtivos: 0,
          alertasRecentes: 0,
          cadastrosHoje: 0,
          verificacoesHoje: 0
        };
      }
    },
    refetchInterval: 30000, // Atualizar a cada 30 segundos
  });
};

// Hook para clientes
export const useClientes = (params?: { page?: number; limit?: number; search?: string; status?: string }) => {
  return useQuery({
    queryKey: ['clientes', params],
    queryFn: async () => {
      const response = await apiService.getClientes(params);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao buscar clientes');
      }
      
      return {
        clientes: response.data || [],
        pagination: response.pagination
      };
    },
  });
};

// Hook para cliente específico
export const useCliente = (id: string) => {
  return useQuery({
    queryKey: ['cliente', id],
    queryFn: async () => {
      const response = await apiService.getCliente(id);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao buscar cliente');
      }
      
      return response.data;
    },
    enabled: !!id,
  });
};

// Hook para motocicletas
export const useMotocicletas = (params?: { 
  page?: number; 
  limit?: number; 
  search?: string; 
  status?: string; 
  marca?: string;
}) => {
  return useQuery({
    queryKey: ['motocicletas', params],
    queryFn: async () => {
      const response = await apiService.getMotocicletas(params);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao buscar motocicletas');
      }
      
      return {
        motocicletas: response.data || [],
        pagination: response.pagination
      };
    },
  });
};

// Hook para motocicleta específica
export const useMotocicleta = (id: string) => {
  return useQuery({
    queryKey: ['motocicleta', id],
    queryFn: async () => {
      const response = await apiService.getMotocicleta(id);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao buscar motocicleta');
      }
      
      return response.data;
    },
    enabled: !!id,
  });
};

// Mutations para clientes
export const useCreateCliente = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (cliente: Omit<Cliente, 'id' | 'dataCadastro' | 'dataAtualizacao'>) => {
      const response = await apiService.createCliente(cliente);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao criar cliente');
      }
      
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clientes'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Cliente criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(apiService.handleApiError(error));
    },
  });
};

export const useUpdateCliente = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Cliente> }) => {
      const response = await apiService.updateCliente(id, data);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao atualizar cliente');
      }
      
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['clientes'] });
      queryClient.invalidateQueries({ queryKey: ['cliente', variables.id] });
      toast.success('Cliente atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(apiService.handleApiError(error));
    },
  });
};

export const useDeleteCliente = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await apiService.deleteCliente(id);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao excluir cliente');
      }
      
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clientes'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Cliente excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(apiService.handleApiError(error));
    },
  });
};

// Mutations para motocicletas
export const useCreateMotocicleta = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (motocicleta: Omit<Motocicleta, 'id' | 'historicoStatus' | 'documentos'>) => {
      const response = await apiService.createMotocicleta(motocicleta);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao criar motocicleta');
      }
      
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['motocicletas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Motocicleta criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(apiService.handleApiError(error));
    },
  });
};

export const useUpdateMotocicleta = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Motocicleta> }) => {
      const response = await apiService.updateMotocicleta(id, data);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao atualizar motocicleta');
      }
      
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['motocicletas'] });
      queryClient.invalidateQueries({ queryKey: ['motocicleta', variables.id] });
      toast.success('Motocicleta atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(apiService.handleApiError(error));
    },
  });
};

export const useDeleteMotocicleta = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await apiService.deleteMotocicleta(id);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao excluir motocicleta');
      }
      
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['motocicletas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Motocicleta excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(apiService.handleApiError(error));
    },
  });
};

export const useUpdateMotocicletaStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, status, motivo }: { id: string; status: string; motivo?: string }) => {
      const response = await apiService.updateMotocicletaStatus(id, status, motivo);
      
      if (!response.success) {
        throw new Error(response.message || 'Erro ao atualizar status da motocicleta');
      }
      
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['motocicletas'] });
      queryClient.invalidateQueries({ queryKey: ['motocicleta', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Status da motocicleta atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(apiService.handleApiError(error));
    },
  });
};

// Hook para testar conexão com a API
export const useApiConnection = () => {
  return useQuery({
    queryKey: ['api-connection'],
    queryFn: () => apiService.testConnection(),
    refetchInterval: 60000, // Testar a cada minuto
    retry: 3,
  });
};
