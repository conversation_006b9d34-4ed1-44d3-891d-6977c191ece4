import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';
import {
  Download,
  Calendar,
  TrendingUp,
  Users,
  Bike,
  AlertTriangle,
  CheckCircle,
  FileText,
  Brain,
  RefreshCw,
  Sparkles,
  Target
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON><PERSON><PERSON>, <PERSON>tocicle<PERSON> } from '../types';
import geminiService, { AnalysisResponse } from '../services/geminiService';
import AIAnalysis from '../components/AIAnalysis';
import toast from 'react-hot-toast';

// Mock data for charts
const monthlyData = [
  { month: 'Jan', clientes: 65, motos: 78, regulares: 70, irregulares: 8 },
  { month: 'Fev', clientes: 89, motos: 95, regulares: 85, irregulares: 10 },
  { month: 'Mar', clientes: 102, motos: 118, regulares: 105, irregulares: 13 },
  { month: 'Abr', clientes: 78, motos: 89, regulares: 80, irregulares: 9 },
  { month: 'Mai', clientes: 95, motos: 112, regulares: 98, irregulares: 14 },
  { month: 'Jun', clientes: 123, motos: 145, regulares: 130, irregulares: 15 },
];

const statusDistribution = [
  { name: 'Regular', value: 1156, color: '#10B981' },
  { name: 'Irregular', value: 233, color: '#EF4444' },
  { name: 'Pendente', value: 45, color: '#F59E0B' },
  { name: 'Bloqueada', value: 12, color: '#DC2626' },
];

const brandData = [
  { brand: 'Honda', count: 450 },
  { brand: 'Yamaha', count: 320 },
  { brand: 'Kawasaki', count: 280 },
  { brand: 'Suzuki', count: 190 },
  { brand: 'BMW', count: 85 },
  { brand: 'Outros', count: 120 },
];

const Relatorios: React.FC = () => {
  const [dateRange, setDateRange] = useState('6months');
  const [reportType, setReportType] = useState('geral');
  const [selectedAnalysisType, setSelectedAnalysisType] = useState<string>('general');
  const [currentAnalysis, setCurrentAnalysis] = useState<AnalysisResponse | null>(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  const geminiStatus = geminiService.getStatus();

  const generateAnalysis = async () => {
    if (!geminiStatus.available) {
      toast.error('Gemini AI não está configurado. Verifique a chave da API.');
      return;
    }

    setAnalysisLoading(true);
    setAnalysisError(null);

    try {
      // Mock data for analysis
      const mockClientes: Cliente[] = [];
      const mockMotos: Motocicleta[] = [];

      let analysis: AnalysisResponse;

      switch (selectedAnalysisType) {
        case 'clients':
          analysis = await geminiService.analyzeClients(mockClientes);
          break;
        case 'motorcycles':
          analysis = await geminiService.analyzeMotorcycles(mockMotos);
          break;
        case 'general':
          analysis = await geminiService.generateGeneralReport(mockClientes, mockMotos);
          break;
        default:
          analysis = await geminiService.generateGeneralReport(mockClientes, mockMotos);
      }

      setCurrentAnalysis(analysis);
      toast.success('Análise gerada com sucesso!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setAnalysisError(errorMessage);
      toast.error('Erro ao gerar análise: ' + errorMessage);
    } finally {
      setAnalysisLoading(false);
    }
  };

  const analysisTypes = [
    { id: 'general', name: 'Relatório Geral', icon: FileText, color: 'blue' },
    { id: 'clients', name: 'Análise de Clientes', icon: Users, color: 'green' },
    { id: 'motorcycles', name: 'Análise de Motocicletas', icon: Bike, color: 'purple' },
    { id: 'trends', name: 'Análise de Tendências', icon: TrendingUp, color: 'orange' }
  ];

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    change?: string;
  }> = ({ title, value, icon, color, change }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${color}`}>
          {icon}
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {change && (
            <p className="text-sm text-green-600 flex items-center mt-1">
              <TrendingUp className="h-4 w-4 mr-1" />
              {change}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Relatórios e Análises</h1>
        <div className="flex items-center space-x-4">
          <select
            className="input"
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
          >
            <option value="1month">Último mês</option>
            <option value="3months">Últimos 3 meses</option>
            <option value="6months">Últimos 6 meses</option>
            <option value="1year">Último ano</option>
          </select>
          <button className="btn-primary flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <span>Exportar</span>
          </button>
        </div>
      </div>

      {/* Report Type Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'geral', name: 'Visão Geral' },
              { id: 'clientes', name: 'Clientes' },
              { id: 'motos', name: 'Motocicletas' },
              { id: 'status', name: 'Status' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setReportType(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  reportType === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total de Clientes"
          value="1,247"
          icon={<Users className="h-6 w-6 text-white" />}
          color="bg-blue-500"
          change="+12% este mês"
        />
        <StatCard
          title="Total de Motos"
          value="1,389"
          icon={<Bike className="h-6 w-6 text-white" />}
          color="bg-purple-500"
          change="+8% este mês"
        />
        <StatCard
          title="Taxa de Regularidade"
          value="83.2%"
          icon={<CheckCircle className="h-6 w-6 text-white" />}
          color="bg-green-500"
          change="+2.1% este mês"
        />
        <StatCard
          title="Alertas Ativos"
          value="45"
          icon={<AlertTriangle className="h-6 w-6 text-white" />}
          color="bg-red-500"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Growth Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Crescimento Mensal</h3>
            <Calendar className="h-5 w-5 text-gray-400" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="clientes" stroke="#3B82F6" strokeWidth={2} name="Clientes" />
              <Line type="monotone" dataKey="motos" stroke="#8B5CF6" strokeWidth={2} name="Motos" />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Status Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Distribuição de Status</h3>
            <Bike className="h-5 w-5 text-gray-400" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={statusDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {statusDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Brand Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Marcas Mais Populares</h3>
            <BarChart className="h-5 w-5 text-gray-400" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={brandData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="brand" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Status Trend */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Tendência de Regularidade</h3>
            <TrendingUp className="h-5 w-5 text-gray-400" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="regulares" stroke="#10B981" strokeWidth={2} name="Regulares" />
              <Line type="monotone" dataKey="irregulares" stroke="#EF4444" strokeWidth={2} name="Irregulares" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Detailed Reports */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Relatórios Detalhados</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-blue-500" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Relatório de Clientes</p>
                  <p className="text-sm text-gray-500">Lista completa com detalhes</p>
                </div>
              </div>
              <Download className="h-5 w-5 text-gray-400" />
            </button>

            <button className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-purple-500" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Relatório de Motos</p>
                  <p className="text-sm text-gray-500">Status e documentação</p>
                </div>
              </div>
              <Download className="h-5 w-5 text-gray-400" />
            </button>

            <button className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-red-500" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Relatório de Irregularidades</p>
                  <p className="text-sm text-gray-500">Motos com problemas</p>
                </div>
              </div>
              <Download className="h-5 w-5 text-gray-400" />
            </button>
          </div>
        </div>
      </div>

      {/* AI Analysis Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Analysis Controls */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Brain className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Análise Inteligente</h3>
                <p className="text-sm text-gray-500">Powered by Gemini AI</p>
              </div>
            </div>
            {geminiStatus.available ? (
              <div className="flex items-center gap-2 text-green-600 text-sm">
                <Sparkles className="h-4 w-4" />
                <span>IA Ativa</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertTriangle className="h-4 w-4" />
                <span>IA Inativa</span>
              </div>
            )}
          </div>
          <div className="card-body space-y-4">
            {/* Analysis Type Selection */}
            <div>
              <label className="label">Tipo de Análise</label>
              <div className="space-y-2">
                {analysisTypes.map((type) => (
                  <button
                    key={type.id}
                    type="button"
                    onClick={() => setSelectedAnalysisType(type.id)}
                    className={`w-full p-3 rounded-lg border-2 transition-all ${
                      selectedAnalysisType === type.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-gray-100">
                        <type.icon className="h-4 w-4" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium text-gray-900">{type.name}</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Generate Button */}
            <button
              type="button"
              onClick={generateAnalysis}
              disabled={analysisLoading || !geminiStatus.available}
              className="btn-primary w-full flex items-center justify-center gap-2"
            >
              {analysisLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Gerando...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4" />
                  Gerar Análise
                </>
              )}
            </button>

            {!geminiStatus.available && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">IA não configurada</p>
                    <p>{geminiStatus.message}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Analysis Results */}
        <div className="lg:col-span-2">
          <AIAnalysis
            analysis={currentAnalysis}
            loading={analysisLoading}
            error={analysisError}
            onRefresh={generateAnalysis}
            title={analysisTypes.find(t => t.id === selectedAnalysisType)?.name || 'Análise'}
          />
        </div>
      </div>
    </div>
  );
};

export default Relatorios;
