# 🗄️ Configuração do Banco de Dados - Alfa Motos CRM

Este guia explica como configurar o MySQL e phpMyAdmin para o sistema Alfa Motos CRM.

## 📋 Pré-requisitos

- MySQL Server 8.0 ou superior
- phpMyAdmin (opcional, mas recomendado)
- Node.js 18+ com npm

## 🚀 Instalação Rápida

### 1. Instalar MySQL e phpMyAdmin

#### Windows (XAMPP - Recomendado)
1. Baixe e instale o [XAMPP](https://www.apachefriends.org/download.html)
2. Inicie o Apache e MySQL no painel de controle do XAMPP
3. Acesse phpMyAdmin em: http://localhost/phpmyadmin

#### Windows (MySQL Standalone)
1. Baixe o [MySQL Installer](https://dev.mysql.com/downloads/installer/)
2. Instale MySQL Server e MySQL Workbench
3. Configure a senha do usuário root

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install mysql-server phpmyadmin
sudo mysql_secure_installation
```

#### macOS (Homebrew)
```bash
brew install mysql
brew services start mysql
# Para phpMyAdmin
brew install phpmyadmin
```

### 2. Configurar o Banco de Dados

#### Opção A: Via phpMyAdmin (Mais Fácil)
1. Acesse http://localhost/phpmyadmin
2. Faça login com usuário `root` e sua senha
3. Clique em "Importar"
4. Selecione o arquivo `database/schema.sql`
5. Clique em "Executar"
6. Repita o processo com `database/sample_data.sql`

#### Opção B: Via Linha de Comando
```bash
# Navegar para o diretório do projeto
cd alfa-motos-crm

# Criar o banco e estrutura
mysql -u root -p < database/schema.sql

# Inserir dados de exemplo
mysql -u root -p alfa_motos_crm < database/sample_data.sql
```

### 3. Configurar Variáveis de Ambiente

1. Copie o arquivo de exemplo:
```bash
cp .env.example .env
```

2. Edite o arquivo `.env` com suas configurações:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=alfa_motos_crm
DB_USER=root
DB_PASSWORD=sua_senha_mysql

# API Configuration
API_PORT=3001
JWT_SECRET=seu_jwt_secret_aqui
BCRYPT_ROUNDS=10

# Frontend Configuration
VITE_API_URL=http://localhost:3001/api
```

### 4. Instalar Dependências e Iniciar

```bash
# Instalar dependências
npm install

# Iniciar o servidor backend
npm run server:dev

# Em outro terminal, iniciar o frontend
npm run dev
```

## 🔧 Estrutura do Banco de Dados

### Tabelas Principais

- **users** - Usuários do sistema (admin/user)
- **clientes** - Dados dos clientes
- **motocicletas** - Dados das motocicletas
- **documentos** - Documentos das motocicletas
- **historico_status** - Histórico de mudanças de status
- **consultas_api** - Log de consultas às APIs externas
- **notificacoes** - Sistema de notificações
- **configuracoes** - Configurações do sistema

### Relacionamentos

```
clientes (1) -----> (N) motocicletas
motocicletas (1) -> (N) documentos
motocicletas (1) -> (N) historico_status
motocicletas (1) -> (N) consultas_api
```

## 🔐 Usuários Padrão

O sistema vem com dois usuários pré-configurados:

- **Admin:** `<EMAIL>` / `admin123`
- **User:** `<EMAIL>` / `user123`

## 📊 Dados de Exemplo

O arquivo `sample_data.sql` inclui:
- 11 clientes (pessoas físicas e jurídicas)
- 15 motocicletas (várias marcas e status)
- Documentos associados
- Histórico de status
- Consultas de API simuladas
- Notificações de exemplo

## 🛠️ Scripts Úteis

```bash
# Backup do banco
mysqldump -u root -p alfa_motos_crm > backup.sql

# Restaurar backup
mysql -u root -p alfa_motos_crm < backup.sql

# Resetar dados (cuidado!)
mysql -u root -p -e "DROP DATABASE IF EXISTS alfa_motos_crm;"
mysql -u root -p < database/schema.sql
mysql -u root -p alfa_motos_crm < database/sample_data.sql
```

## 🔍 Verificação da Instalação

1. **Teste a conexão do banco:**
```bash
mysql -u root -p -e "USE alfa_motos_crm; SELECT COUNT(*) FROM clientes;"
```

2. **Teste a API:**
```bash
curl http://localhost:3001/health
```

3. **Acesse o sistema:**
- Frontend: http://localhost:3000
- API: http://localhost:3001/api
- phpMyAdmin: http://localhost/phpmyadmin

## ⚠️ Solução de Problemas

### Erro de Conexão MySQL
```bash
# Verificar se MySQL está rodando
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Reiniciar MySQL
sudo systemctl restart mysql  # Linux
brew services restart mysql  # macOS
```

### Erro de Permissão
```sql
-- Criar usuário específico para a aplicação
CREATE USER 'alfamotos'@'localhost' IDENTIFIED BY 'senha_segura';
GRANT ALL PRIVILEGES ON alfa_motos_crm.* TO 'alfamotos'@'localhost';
FLUSH PRIVILEGES;
```

### Porta 3001 em Uso
```bash
# Verificar o que está usando a porta
netstat -tulpn | grep 3001  # Linux
lsof -i :3001  # macOS

# Alterar a porta no .env
API_PORT=3002
```

## 📈 Performance

Para melhor performance em produção:

1. **Configurar índices adicionais:**
```sql
CREATE INDEX idx_cliente_nome_email ON clientes(nome, email);
CREATE INDEX idx_moto_placa_status ON motocicletas(placa, status);
```

2. **Configurar cache de consultas:**
```sql
SET GLOBAL query_cache_size = 268435456;  -- 256MB
SET GLOBAL query_cache_type = ON;
```

3. **Backup automático:**
```bash
# Adicionar ao crontab
0 2 * * * mysqldump -u root -p alfa_motos_crm > /backup/alfa_motos_$(date +\%Y\%m\%d).sql
```

## 🔒 Segurança

- Altere as senhas padrão em produção
- Configure SSL para conexões MySQL
- Use variáveis de ambiente para credenciais
- Implemente backup regular
- Configure firewall adequadamente

## 📞 Suporte

Se encontrar problemas:
1. Verifique os logs do MySQL: `/var/log/mysql/error.log`
2. Verifique os logs da aplicação no console
3. Teste a conectividade: `telnet localhost 3306`
4. Verifique as permissões do usuário MySQL
